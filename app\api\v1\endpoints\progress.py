from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.core.deps import get_current_user
from app.db.models import User
from app.schemas.progress import ProgressCreate, ProgressResponse, ProgressSummary
from app.services.progress_service import ProgressService
from typing import List

router = APIRouter()

@router.post("/save", response_model=ProgressResponse, status_code=status.HTTP_201_CREATED)
async def save_progress(
    progress_data: ProgressCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Save user's learning progress."""
    try:
        progress = ProgressService.save_progress(db, current_user, progress_data)
        return progress
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save progress: {str(e)}"
        )

@router.get("/fetch", response_model=List[ProgressResponse])
async def fetch_user_progress(
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Fetch user's learning progress."""
    try:
        progress_records = ProgressService.get_user_progress(db, current_user.id, limit)
        return progress_records
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch progress: {str(e)}"
        )

@router.get("/fetch/{user_id}", response_model=List[ProgressResponse])
async def fetch_specific_user_progress(
    user_id: int,
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Fetch specific user's learning progress (for admin or self)."""
    try:
        # Check if user is requesting their own progress or is admin
        if current_user.id != user_id:
            # In a real app, you'd check for admin role here
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to view this user's progress"
            )
        
        progress_records = ProgressService.get_user_progress(db, user_id, limit)
        return progress_records
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch progress: {str(e)}"
        )

@router.get("/summary", response_model=ProgressSummary)
async def get_progress_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive progress summary."""
    try:
        summary = ProgressService.get_progress_summary(db, current_user.id)
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get progress summary: {str(e)}"
        )

@router.get("/subject/{subject}", response_model=List[ProgressResponse])
async def get_progress_by_subject(
    subject: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's progress for a specific subject."""
    try:
        progress_records = ProgressService.get_progress_by_subject(db, current_user.id, subject)
        return progress_records
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get subject progress: {str(e)}"
        )

@router.get("/statistics")
async def get_progress_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed progress statistics."""
    try:
        subject_stats = ProgressService.get_subject_statistics(db, current_user.id)
        learning_streak = ProgressService.get_learning_streak(db, current_user.id)
        
        return {
            "subject_statistics": subject_stats,
            "learning_streak": learning_streak
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get statistics: {str(e)}"
        )
