# SkillCred - AI Educational Tutor Platform

A complete AI-powered educational platform with a modern web frontend and robust API backend that provides step-by-step tutoring, interactive quizzes, and personalized learning recommendations.

## 🌟 Features

### 🎓 Learning Experience
- **AI-Powered Tutoring**: Step-by-step lessons using OpenAI GPT
- **Interactive Quizzes**: Dynamic quiz generation with instant feedback
- **Progress Tracking**: Comprehensive learning analytics and statistics
- **Smart Recommendations**: AI-driven next topic suggestions
- **Multi-Subject Support**: Mathematics, Science, English, History, and more

### 💻 Technical Features
- **Modern Web Frontend**: Responsive, mobile-friendly interface
- **RESTful API**: Complete backend with comprehensive documentation
- **User Authentication**: Secure JWT-based authentication system
- **Real-time Updates**: Live progress tracking and recommendations
- **Cross-Platform**: Works on desktop, tablet, and mobile devices

## 🏗️ Architecture

### Backend (FastAPI)
- **Database**: SQLite with SQLAlchemy ORM
- **AI Engine**: OpenAI GPT API integration
- **Authentication**: JWT tokens with bcrypt password hashing
- **API Documentation**: Automatic Swagger/OpenAPI documentation

### Frontend (Modern Web App)
- **Technology**: Pure HTML5, CSS3, JavaScript (ES6+)
- **Design**: Modern, clean, responsive interface
- **Features**: Real-time API integration, interactive dashboard
- **Compatibility**: All modern browsers

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- OpenAI API key (optional for basic functionality)

### 1. Setup Backend API

```bash
# Create and activate virtual environment
python -m venv venv
.\venv\Scripts\Activate.ps1  # Windows PowerShell
# source venv/bin/activate    # Linux/Mac

# Install dependencies
pip install -r requirements.txt

# Setup environment and database
python setup.py

# Start the API server
python run.py
```

The API will be available at: http://localhost:8000

### 2. Start Frontend Website

```bash
# In a new terminal, navigate to frontend directory
cd frontend

# Start the web server
python server.py
```

The website will open automatically at: http://localhost:3000

### 3. Access the Application

- **Website**: http://localhost:3000 (Main application)
- **API Documentation**: http://localhost:8000/docs (Swagger UI)
- **API Alternative Docs**: http://localhost:8000/redoc (ReDoc)

## 🎯 How to Use

### 1. **Registration & Login**
- Visit http://localhost:3000
- Click "Get Started" to register or "Login" if you have an account
- Choose your learning level (Beginner, Intermediate, Advanced)

### 2. **Dashboard Overview**
- View your learning statistics and progress
- See personalized AI recommendations
- Track your learning streak and achievements

### 3. **Start Learning**
- Select a subject (Mathematics, Science, English, etc.)
- Enter a specific topic you want to learn
- Click "Start Lesson" to begin

### 4. **Interactive Learning**
- Read AI-generated lesson content tailored to your level
- Take interactive quizzes with multiple choice and text questions
- Get instant feedback and detailed explanations
- Your progress is automatically saved

### 5. **Track Progress**
- Monitor your learning journey in the dashboard
- View detailed statistics by subject
- Follow AI recommendations for optimal learning path

## 📚 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login

### Tutoring System
- `POST /api/v1/tutor/lesson/start` - Start a new lesson
- `POST /api/v1/tutor/lesson/next` - Get next lesson content

### Quiz System
- `POST /api/v1/quiz/submit` - Submit quiz answers
- `GET /api/v1/quiz/results/{quiz_id}` - Get quiz results
- `GET /api/v1/quiz/history` - Get quiz history

### Progress Tracking
- `POST /api/v1/progress/save` - Save learning progress
- `GET /api/v1/progress/summary` - Get progress summary
- `GET /api/v1/progress/statistics` - Get detailed statistics

### Recommendations
- `GET /api/v1/recommend/next` - Get next topic recommendations
- `POST /api/v1/recommend/generate` - Generate new recommendation

## Database Schema

The application uses SQLite with the following tables:
- **Users**: User account information
- **Progress**: Learning progress tracking
- **QuizResults**: Quiz scores and answers
- **Recommendations**: Personalized topic suggestions

## Frontend Integration

This API is designed to work seamlessly with no-code platforms:
- **Glide**: Mobile-first app builder
- **Bubble.io**: Web application builder

See the `docs/frontend-integration.md` for detailed integration guides.

## Development

### Running Tests
```bash
pytest
```

### Code Structure
```
app/
├── api/v1/          # API routes
├── core/            # Configuration
├── db/              # Database models
├── schemas/         # Pydantic models
└── services/        # Business logic
```

## Deployment

The application can be deployed on:
- **Render** (recommended)
- **Railway**
- **Heroku**
- **AWS/GCP/Azure**

## License

MIT License
