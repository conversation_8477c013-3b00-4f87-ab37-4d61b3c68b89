# Educational Tutor API

An AI-powered educational tutor app that provides step-by-step tutoring, quizzes, and personalized recommendations.

## Features

- **AI-Powered Tutoring**: Step-by-step lessons using OpenAI GPT
- **Interactive Quizzes**: Dynamic quiz generation and evaluation
- **Progress Tracking**: Comprehensive learning progress monitoring
- **Personalized Recommendations**: AI-driven next topic suggestions
- **User Authentication**: Secure JWT-based authentication
- **No-Code Frontend Ready**: Designed for integration with Glide/Bubble.io

## Tech Stack

- **Backend**: FastAPI (Python)
- **Database**: SQLite with SQLAlchemy ORM
- **AI Engine**: OpenAI GPT API
- **Authentication**: JWT tokens
- **Frontend**: No-code platforms (Glide/Bubble.io)

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd skillcred-website
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your OpenAI API key and other settings
   ```

4. **Run the Application**
   ```bash
   uvicorn app.main:app --reload
   ```

5. **Access API Documentation**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login

### Tutoring
- `POST /api/v1/tutor/lesson/start` - Start a new lesson
- `POST /api/v1/tutor/lesson/next` - Get next lesson content

### Quizzes
- `POST /api/v1/quiz/submit` - Submit quiz answers
- `GET /api/v1/quiz/results/{quiz_id}` - Get quiz results

### Progress
- `POST /api/v1/progress/save` - Save learning progress
- `GET /api/v1/progress/fetch/{user_id}` - Fetch user progress

### Recommendations
- `GET /api/v1/recommend/next/{user_id}` - Get next topic recommendation

## Database Schema

The application uses SQLite with the following tables:
- **Users**: User account information
- **Progress**: Learning progress tracking
- **QuizResults**: Quiz scores and answers
- **Recommendations**: Personalized topic suggestions

## Frontend Integration

This API is designed to work seamlessly with no-code platforms:
- **Glide**: Mobile-first app builder
- **Bubble.io**: Web application builder

See the `docs/frontend-integration.md` for detailed integration guides.

## Development

### Running Tests
```bash
pytest
```

### Code Structure
```
app/
├── api/v1/          # API routes
├── core/            # Configuration
├── db/              # Database models
├── schemas/         # Pydantic models
└── services/        # Business logic
```

## Deployment

The application can be deployed on:
- **Render** (recommended)
- **Railway**
- **Heroku**
- **AWS/GCP/Azure**

## License

MIT License
