#!/usr/bin/env python3
"""
Educational Tutor API - Development Server
Run this script to start the development server.
"""

import uvicorn
import os
from app.core.config import settings

if __name__ == "__main__":
    # Ensure .env file exists
    if not os.path.exists(".env"):
        print("⚠️  Warning: .env file not found!")
        print("📝 Please copy .env.example to .env and configure your settings.")
        print("🔑 Don't forget to add your OpenAI API key!")
        
    print("🚀 Starting Educational Tutor API...")
    print(f"📚 App Name: {settings.APP_NAME}")
    print(f"🔧 Debug Mode: {settings.DEBUG}")
    print("📖 API Documentation will be available at:")
    print("   • Swagger UI: http://localhost:8000/docs")
    print("   • ReDoc: http://localhost:8000/redoc")
    print("🌐 API Base URL: http://localhost:8000/api/v1")
    print("\n" + "="*50)
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info" if settings.DEBUG else "warning"
    )
