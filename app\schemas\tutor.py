from pydantic import BaseModel
from typing import List, Optional, Dict, Any

class LessonStartRequest(BaseModel):
    subject: str
    topic: str
    user_level: str = "beginner"

class LessonNextRequest(BaseModel):
    subject: str
    topic: str
    user_level: str
    previous_content: Optional[str] = None
    user_response: Optional[str] = None

class LessonResponse(BaseModel):
    content: str
    quiz_questions: List[Dict[str, Any]]
    lesson_id: str
    subject: str
    topic: str

class QuizQuestion(BaseModel):
    question: str
    type: str  # "multiple_choice" or "short_answer"
    options: Optional[List[str]] = None  # For multiple choice
    correct_answer: str

class QuizSubmission(BaseModel):
    quiz_id: str
    subject: str
    topic: str
    answers: Dict[str, str]  # question_id -> user_answer

class QuizEvaluation(BaseModel):
    score: int
    total_questions: int
    correct_count: int
    feedback: str
    strengths: List[str]
    weaknesses: List[str]
    detailed_results: List[Dict[str, Any]]
