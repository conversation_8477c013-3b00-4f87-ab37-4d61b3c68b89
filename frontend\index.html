<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkillCred - AI Educational Tutor</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-graduation-cap"></i>
                <span>SkillCred</span>
            </div>
            <div class="nav-menu" id="navMenu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#dashboard" class="nav-link" id="dashboardLink" style="display: none;">Dashboard</a>
                <a href="#" class="nav-link" id="loginBtn">Login</a>
                <a href="#" class="nav-link" id="logoutBtn" style="display: none;">Logout</a>
            </div>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1>AI-Powered Learning Made Simple</h1>
                <p>Experience personalized tutoring with step-by-step lessons, interactive quizzes, and smart recommendations powered by artificial intelligence.</p>
                <div class="hero-buttons">
                    <button class="btn btn-primary" id="getStartedBtn">Get Started</button>
                    <button class="btn btn-secondary" id="learnMoreBtn">Learn More</button>
                </div>
            </div>
            <div class="hero-image">
                <div class="hero-card">
                    <i class="fas fa-robot"></i>
                    <h3>AI Tutor</h3>
                    <p>Personalized learning experience</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2>Why Choose SkillCred?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <i class="fas fa-brain"></i>
                    <h3>AI-Powered Tutoring</h3>
                    <p>Get personalized lessons adapted to your learning style and pace with our advanced AI tutor.</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-quiz"></i>
                    <h3>Interactive Quizzes</h3>
                    <p>Test your knowledge with dynamic quizzes that provide instant feedback and detailed explanations.</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>Progress Tracking</h3>
                    <p>Monitor your learning journey with comprehensive analytics and progress visualization.</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-lightbulb"></i>
                    <h3>Smart Recommendations</h3>
                    <p>Receive intelligent suggestions for your next learning topics based on your performance.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Auth Modal -->
    <div id="authModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="auth-container">
                <div class="auth-tabs">
                    <button class="tab-btn active" data-tab="login">Login</button>
                    <button class="tab-btn" data-tab="register">Register</button>
                </div>
                
                <!-- Login Form -->
                <div id="loginForm" class="auth-form active">
                    <h2>Welcome Back</h2>
                    <form id="loginFormElement">
                        <div class="form-group">
                            <label for="loginEmail">Email</label>
                            <input type="email" id="loginEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="loginPassword">Password</label>
                            <input type="password" id="loginPassword" required>
                        </div>
                        <button type="submit" class="btn btn-primary full-width">Login</button>
                    </form>
                </div>

                <!-- Register Form -->
                <div id="registerForm" class="auth-form">
                    <h2>Create Account</h2>
                    <form id="registerFormElement">
                        <div class="form-group">
                            <label for="registerName">Full Name</label>
                            <input type="text" id="registerName" required>
                        </div>
                        <div class="form-group">
                            <label for="registerEmail">Email</label>
                            <input type="email" id="registerEmail" required>
                        </div>
                        <div class="form-group">
                            <label for="registerPassword">Password</label>
                            <input type="password" id="registerPassword" required>
                        </div>
                        <div class="form-group">
                            <label for="registerLevel">Learning Level</label>
                            <select id="registerLevel" required>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary full-width">Create Account</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Section -->
    <section id="dashboard" class="dashboard" style="display: none;">
        <div class="container">
            <div class="dashboard-header">
                <h2>Learning Dashboard</h2>
                <div class="user-info">
                    <span id="userName">Welcome!</span>
                    <span id="userLevel" class="level-badge">Beginner</span>
                </div>
            </div>

            <!-- Dashboard Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-book"></i>
                    <div class="stat-info">
                        <h3 id="totalLessons">0</h3>
                        <p>Lessons Completed</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-percentage"></i>
                    <div class="stat-info">
                        <h3 id="averageScore">0%</h3>
                        <p>Average Score</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-fire"></i>
                    <div class="stat-info">
                        <h3 id="currentStreak">0</h3>
                        <p>Day Streak</p>
                    </div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-subjects"></i>
                    <div class="stat-info">
                        <h3 id="subjectCount">0</h3>
                        <p>Subjects</p>
                    </div>
                </div>
            </div>

            <!-- Main Dashboard Content -->
            <div class="dashboard-content">
                <!-- Lesson Section -->
                <div class="dashboard-section">
                    <h3>Start Learning</h3>
                    <div class="lesson-starter">
                        <div class="form-group">
                            <label for="subjectSelect">Subject</label>
                            <select id="subjectSelect">
                                <option value="Mathematics">Mathematics</option>
                                <option value="Science">Science</option>
                                <option value="English">English</option>
                                <option value="History">History</option>
                                <option value="Physics">Physics</option>
                                <option value="Chemistry">Chemistry</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="topicInput">Topic</label>
                            <input type="text" id="topicInput" placeholder="e.g., Basic Arithmetic, Grammar Rules">
                        </div>
                        <button class="btn btn-primary" id="startLessonBtn">Start Lesson</button>
                    </div>
                </div>

                <!-- Recommendations Section -->
                <div class="dashboard-section">
                    <h3>Recommended Topics</h3>
                    <div id="recommendationsContainer" class="recommendations-grid">
                        <div class="loading">Loading recommendations...</div>
                    </div>
                </div>

                <!-- Recent Progress -->
                <div class="dashboard-section">
                    <h3>Recent Progress</h3>
                    <div id="recentProgressContainer" class="progress-list">
                        <div class="loading">Loading progress...</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lesson Modal -->
    <div id="lessonModal" class="modal">
        <div class="modal-content lesson-modal">
            <span class="close">&times;</span>
            <div class="lesson-container">
                <div class="lesson-header">
                    <h2 id="lessonTitle">Lesson</h2>
                    <div class="lesson-meta">
                        <span id="lessonSubject">Subject</span> • <span id="lessonTopic">Topic</span>
                    </div>
                </div>
                <div class="lesson-content">
                    <div id="lessonText" class="lesson-text"></div>
                    <div id="quizContainer" class="quiz-container" style="display: none;">
                        <h3>Quiz Time!</h3>
                        <div id="quizQuestions"></div>
                        <button class="btn btn-primary" id="submitQuizBtn">Submit Quiz</button>
                    </div>
                    <div id="quizResults" class="quiz-results" style="display: none;">
                        <h3>Quiz Results</h3>
                        <div id="quizScore"></div>
                        <div id="quizFeedback"></div>
                        <button class="btn btn-secondary" id="nextLessonBtn">Continue Learning</button>
                        <button class="btn btn-primary" id="finishLessonBtn">Finish Lesson</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner"></div>
        <p>Processing...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="script.js"></script>
</body>
</html>
