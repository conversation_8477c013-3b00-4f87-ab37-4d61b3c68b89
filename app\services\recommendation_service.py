from sqlalchemy.orm import Session
from sqlalchemy import desc
from app.db.models import Recommendation, Progress, User
from app.schemas.progress import RecommendationCreate, RecommendationResponse
from app.services.ai_service import AIService
from typing import List, Dict, Any

class RecommendationService:
    def __init__(self):
        self.ai_service = AIService()
    
    async def generate_recommendation(self, db: Session, user: User) -> Recommendation:
        """Generate AI-powered recommendation for user."""
        try:
            # Get user's recent progress
            recent_progress = db.query(Progress).filter(
                Progress.user_id == user.id
            ).order_by(desc(Progress.timestamp)).limit(20).all()
            
            # Convert progress to format suitable for AI
            progress_data = []
            for p in recent_progress:
                progress_data.append({
                    'subject': p.subject,
                    'topic': p.topic,
                    'score': p.score,
                    'timestamp': p.timestamp.isoformat()
                })
            
            # Generate recommendation using AI
            recommendation_data = await self.ai_service.generate_recommendation(
                user_progress=progress_data,
                current_level=user.level
            )
            
            # Create recommendation record
            recommendation = Recommendation(
                user_id=user.id,
                next_topic=recommendation_data["next_topic"],
                subject=recommendation_data["subject"],
                reason=recommendation_data["reason"],
                priority=recommendation_data["priority"]
            )
            
            db.add(recommendation)
            db.commit()
            db.refresh(recommendation)
            
            return recommendation
            
        except Exception as e:
            db.rollback()
            raise e
    
    @staticmethod
    def get_user_recommendations(db: Session, user_id: int, limit: int = 10) -> List[Recommendation]:
        """Get user's recommendations."""
        return db.query(Recommendation).filter(
            Recommendation.user_id == user_id,
            Recommendation.is_completed == 0
        ).order_by(
            Recommendation.priority.asc(),
            desc(Recommendation.created_at)
        ).limit(limit).all()
    
    @staticmethod
    def mark_recommendation_completed(db: Session, recommendation_id: int, user_id: int) -> bool:
        """Mark a recommendation as completed."""
        try:
            recommendation = db.query(Recommendation).filter(
                Recommendation.id == recommendation_id,
                Recommendation.user_id == user_id
            ).first()
            
            if recommendation:
                recommendation.is_completed = 1
                db.commit()
                return True
            return False
            
        except Exception as e:
            db.rollback()
            return False
    
    async def get_smart_recommendations(self, db: Session, user: User) -> List[Dict[str, Any]]:
        """Get intelligent recommendations based on multiple factors."""
        try:
            # Get user's progress statistics
            progress_records = db.query(Progress).filter(Progress.user_id == user.id).all()
            
            if not progress_records:
                # New user recommendations
                return self._get_beginner_recommendations(user.level)
            
            # Analyze user's performance
            subject_performance = {}
            for progress in progress_records:
                subject = progress.subject
                if subject not in subject_performance:
                    subject_performance[subject] = {'scores': [], 'topics': set()}
                
                subject_performance[subject]['scores'].append(progress.score)
                subject_performance[subject]['topics'].add(progress.topic)
            
            # Calculate average scores per subject
            recommendations = []
            for subject, data in subject_performance.items():
                avg_score = sum(data['scores']) / len(data['scores'])
                topic_count = len(data['topics'])
                
                # Determine recommendation type based on performance
                if avg_score < 60:
                    # Struggling - recommend review
                    recommendations.append({
                        'type': 'review',
                        'subject': subject,
                        'next_topic': f"Review {subject} fundamentals",
                        'reason': f"Average score of {avg_score:.1f}% suggests reviewing basics",
                        'priority': 1
                    })
                elif avg_score > 80 and topic_count >= 3:
                    # Excelling - recommend advanced topics
                    recommendations.append({
                        'type': 'advance',
                        'subject': subject,
                        'next_topic': f"Advanced {subject} concepts",
                        'reason': f"Strong performance ({avg_score:.1f}%) indicates readiness for advanced topics",
                        'priority': 2
                    })
                else:
                    # Steady progress - continue with next topics
                    recommendations.append({
                        'type': 'continue',
                        'subject': subject,
                        'next_topic': f"Next {subject} topic",
                        'reason': f"Steady progress ({avg_score:.1f}%) - continue learning",
                        'priority': 2
                    })
            
            # Add AI-generated recommendation
            try:
                ai_recommendation = await self.generate_recommendation(db, user)
                recommendations.append({
                    'type': 'ai_generated',
                    'subject': ai_recommendation.subject,
                    'next_topic': ai_recommendation.next_topic,
                    'reason': ai_recommendation.reason,
                    'priority': ai_recommendation.priority
                })
            except Exception:
                pass  # Continue without AI recommendation if it fails
            
            # Sort by priority and return top recommendations
            recommendations.sort(key=lambda x: x['priority'])
            return recommendations[:5]
            
        except Exception as e:
            # Fallback to basic recommendations
            return self._get_beginner_recommendations(user.level)
    
    def _get_beginner_recommendations(self, level: str) -> List[Dict[str, Any]]:
        """Get basic recommendations for new users."""
        if level == "beginner":
            return [
                {
                    'type': 'starter',
                    'subject': 'Mathematics',
                    'next_topic': 'Basic Arithmetic',
                    'reason': 'Great starting point for building math foundations',
                    'priority': 1
                },
                {
                    'type': 'starter',
                    'subject': 'Science',
                    'next_topic': 'Introduction to Science',
                    'reason': 'Explore the basics of scientific thinking',
                    'priority': 2
                },
                {
                    'type': 'starter',
                    'subject': 'English',
                    'next_topic': 'Reading Comprehension',
                    'reason': 'Essential skill for all learning',
                    'priority': 1
                }
            ]
        elif level == "intermediate":
            return [
                {
                    'type': 'intermediate',
                    'subject': 'Mathematics',
                    'next_topic': 'Algebra Basics',
                    'reason': 'Build on arithmetic foundations',
                    'priority': 1
                },
                {
                    'type': 'intermediate',
                    'subject': 'Science',
                    'next_topic': 'Physics Fundamentals',
                    'reason': 'Explore the physical world',
                    'priority': 2
                }
            ]
        else:  # advanced
            return [
                {
                    'type': 'advanced',
                    'subject': 'Mathematics',
                    'next_topic': 'Calculus Introduction',
                    'reason': 'Advanced mathematical concepts',
                    'priority': 1
                },
                {
                    'type': 'advanced',
                    'subject': 'Science',
                    'next_topic': 'Advanced Chemistry',
                    'reason': 'Complex scientific principles',
                    'priority': 2
                }
            ]
