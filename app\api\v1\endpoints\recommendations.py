from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.core.deps import get_current_user
from app.db.models import User
from app.schemas.progress import RecommendationResponse
from app.services.recommendation_service import RecommendationService
from typing import List

router = APIRouter()
recommendation_service = RecommendationService()

@router.get("/next", response_model=List[dict])
async def get_next_recommendations(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get intelligent next topic recommendations for user."""
    try:
        recommendations = await recommendation_service.get_smart_recommendations(db, current_user)
        return recommendations
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}"
        )

@router.get("/next/{user_id}", response_model=List[dict])
async def get_user_recommendations(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recommendations for a specific user."""
    try:
        # Check if user is requesting their own recommendations
        if current_user.id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to view this user's recommendations"
            )
        
        recommendations = await recommendation_service.get_smart_recommendations(db, current_user)
        return recommendations
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}"
        )

@router.post("/generate", response_model=RecommendationResponse)
async def generate_new_recommendation(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a new AI-powered recommendation."""
    try:
        recommendation = await recommendation_service.generate_recommendation(db, current_user)
        return recommendation
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate recommendation: {str(e)}"
        )

@router.get("/history", response_model=List[RecommendationResponse])
async def get_recommendation_history(
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's recommendation history."""
    try:
        recommendations = RecommendationService.get_user_recommendations(db, current_user.id, limit)
        return recommendations
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendation history: {str(e)}"
        )

@router.put("/complete/{recommendation_id}")
async def mark_recommendation_complete(
    recommendation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark a recommendation as completed."""
    try:
        success = RecommendationService.mark_recommendation_completed(
            db, recommendation_id, current_user.id
        )
        
        if success:
            return {"message": "Recommendation marked as completed"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Recommendation not found"
            )
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update recommendation: {str(e)}"
        )
