// API Configuration
const API_BASE_URL = 'http://localhost:8000/api/v1';

// Global State
let currentUser = null;
let authToken = null;
let currentLessonData = null;
let currentQuizData = null;

// DOM Elements
const authModal = document.getElementById('authModal');
const lessonModal = document.getElementById('lessonModal');
const loadingOverlay = document.getElementById('loadingOverlay');
const toastContainer = document.getElementById('toastContainer');

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    checkAuthStatus();
});

function initializeApp() {
    // Check if user is logged in
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');
    
    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        showDashboard();
    }
}

function setupEventListeners() {
    // Navigation
    document.getElementById('loginBtn').addEventListener('click', () => showAuthModal('login'));
    document.getElementById('getStartedBtn').addEventListener('click', () => showAuthModal('register'));
    document.getElementById('learnMoreBtn').addEventListener('click', () => {
        document.getElementById('features').scrollIntoView({ behavior: 'smooth' });
    });
    document.getElementById('logoutBtn').addEventListener('click', logout);

    // Navigation links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const href = link.getAttribute('href');
            if (href === '#home') {
                showHome();
            } else if (href === '#features') {
                showFeatures();
            } else if (href === '#dashboard') {
                if (currentUser) {
                    showDashboard();
                } else {
                    showAuthModal('login');
                }
            }
        });
    });
    
    // Modal controls
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', closeModals);
    });
    
    // Auth tabs
    document.querySelectorAll('.tab-btn').forEach(tab => {
        tab.addEventListener('click', (e) => switchAuthTab(e.target.dataset.tab));
    });
    
    // Auth forms
    document.getElementById('loginFormElement').addEventListener('submit', handleLogin);
    document.getElementById('registerFormElement').addEventListener('submit', handleRegister);
    
    // Dashboard actions
    document.getElementById('startLessonBtn').addEventListener('click', startLesson);
    document.getElementById('submitQuizBtn').addEventListener('click', submitQuiz);
    document.getElementById('nextLessonBtn').addEventListener('click', continueLesson);
    document.getElementById('finishLessonBtn').addEventListener('click', finishLesson);
    
    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            closeModals();
        }
    });

    // Hamburger menu toggle
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('navMenu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
}

// Authentication Functions
function showAuthModal(tab = 'login') {
    authModal.style.display = 'block';
    switchAuthTab(tab);
}

function switchAuthTab(tab) {
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
    
    document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
    document.getElementById(`${tab}Form`).classList.add('active');
}

async function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            authToken = data.access_token;
            currentUser = data.user;
            
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            showToast('Login successful!', 'success');
            closeModals();
            showDashboard();
        } else {
            showToast(data.detail || 'Login failed', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
    }
    
    showLoading(false);
}

async function handleRegister(e) {
    e.preventDefault();
    
    const name = document.getElementById('registerName').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const level = document.getElementById('registerLevel').value;
    
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/auth/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name, email, password, level })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showToast('Registration successful! Please login.', 'success');
            switchAuthTab('login');
            document.getElementById('loginEmail').value = email;
        } else {
            showToast(data.detail || 'Registration failed', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
    }
    
    showLoading(false);
}

function logout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    
    showToast('Logged out successfully', 'success');
    hideDashboard();
}

function checkAuthStatus() {
    const loginBtn = document.getElementById('loginBtn');
    const logoutBtn = document.getElementById('logoutBtn');
    const dashboardLink = document.getElementById('dashboardLink');
    
    if (currentUser) {
        loginBtn.style.display = 'none';
        logoutBtn.style.display = 'block';
        dashboardLink.style.display = 'block';
    } else {
        loginBtn.style.display = 'block';
        logoutBtn.style.display = 'none';
        dashboardLink.style.display = 'none';
    }
}

// Navigation Functions
function showHome() {
    document.getElementById('home').style.display = 'block';
    document.getElementById('features').style.display = 'block';
    document.getElementById('dashboard').style.display = 'none';
    updateNavigation('home');
}

function showFeatures() {
    document.getElementById('home').style.display = 'block';
    document.getElementById('features').style.display = 'block';
    document.getElementById('dashboard').style.display = 'none';
    updateNavigation('features');
    setTimeout(() => {
        document.getElementById('features').scrollIntoView({ behavior: 'smooth' });
    }, 100);
}

// Dashboard Functions
function showDashboard() {
    document.getElementById('home').style.display = 'none';
    document.getElementById('features').style.display = 'none';
    document.getElementById('dashboard').style.display = 'block';

    updateUserInfo();
    loadDashboardData();
    updateNavigation('dashboard');
    checkAuthStatus();
}

function hideDashboard() {
    showHome();
}

function updateNavigation(activeSection) {
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
        const href = link.getAttribute('href');
        if (href === `#${activeSection}`) {
            link.classList.add('active');
        }
    });
}

function updateUserInfo() {
    if (currentUser) {
        document.getElementById('userName').textContent = `Welcome, ${currentUser.name}!`;
        document.getElementById('userLevel').textContent = currentUser.level.charAt(0).toUpperCase() + currentUser.level.slice(1);
    }
}

async function loadDashboardData() {
    if (!authToken) return;
    
    try {
        // Load progress summary
        const progressResponse = await fetch(`${API_BASE_URL}/progress/summary`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });
        
        if (progressResponse.ok) {
            const progressData = await progressResponse.json();
            updateProgressStats(progressData);
            displayRecentProgress(progressData.recent_progress);
        }
        
        // Load recommendations
        const recommendationsResponse = await fetch(`${API_BASE_URL}/recommend/next`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });
        
        if (recommendationsResponse.ok) {
            const recommendations = await recommendationsResponse.json();
            displayRecommendations(recommendations);
        }
        
        // Load statistics
        const statsResponse = await fetch(`${API_BASE_URL}/progress/statistics`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });
        
        if (statsResponse.ok) {
            const stats = await statsResponse.json();
            updateStreakInfo(stats.learning_streak);
        }
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function updateProgressStats(data) {
    document.getElementById('totalLessons').textContent = data.total_lessons || 0;
    document.getElementById('averageScore').textContent = `${data.average_score || 0}%`;
    document.getElementById('subjectCount').textContent = data.subjects?.length || 0;
}

function updateStreakInfo(streakData) {
    document.getElementById('currentStreak').textContent = streakData?.current_streak || 0;
}

function displayRecentProgress(progressList) {
    const container = document.getElementById('recentProgressContainer');
    
    if (!progressList || progressList.length === 0) {
        container.innerHTML = '<div class="loading">No progress data yet. Start learning!</div>';
        return;
    }
    
    container.innerHTML = progressList.slice(0, 5).map(progress => `
        <div class="progress-item">
            <div class="progress-info">
                <h4>${progress.subject} - ${progress.topic}</h4>
                <p>${new Date(progress.timestamp).toLocaleDateString()}</p>
            </div>
            <div class="progress-score">${progress.score}%</div>
        </div>
    `).join('');
}

function displayRecommendations(recommendations) {
    const container = document.getElementById('recommendationsContainer');
    
    if (!recommendations || recommendations.length === 0) {
        container.innerHTML = '<div class="loading">No recommendations available</div>';
        return;
    }
    
    container.innerHTML = recommendations.slice(0, 3).map(rec => `
        <div class="recommendation-card" onclick="startRecommendedLesson('${rec.subject}', '${rec.next_topic}')">
            <h4>${rec.next_topic}</h4>
            <p>${rec.reason}</p>
            <div class="recommendation-meta">
                <span>${rec.subject}</span>
                <span class="priority-badge priority-${rec.priority}">Priority ${rec.priority}</span>
            </div>
        </div>
    `).join('');
}

// Lesson Functions
async function startLesson() {
    const subject = document.getElementById('subjectSelect').value;
    const topic = document.getElementById('topicInput').value.trim();
    
    if (!topic) {
        showToast('Please enter a topic', 'error');
        return;
    }
    
    await startLessonWithParams(subject, topic);
}

function startRecommendedLesson(subject, topic) {
    startLessonWithParams(subject, topic);
}

async function startLessonWithParams(subject, topic) {
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/tutor/lesson/start`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                subject,
                topic,
                user_level: currentUser.level
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentLessonData = data;
            displayLesson(data);
            lessonModal.style.display = 'block';
        } else {
            showToast(data.detail || 'Failed to start lesson', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
    }
    
    showLoading(false);
}

function displayLesson(lessonData) {
    document.getElementById('lessonTitle').textContent = `${lessonData.subject} Lesson`;
    document.getElementById('lessonSubject').textContent = lessonData.subject;
    document.getElementById('lessonTopic').textContent = lessonData.topic;
    document.getElementById('lessonText').innerHTML = lessonData.content.replace(/\n/g, '<br>');
    
    // Store quiz questions for later submission
    if (lessonData.quiz_questions && lessonData.quiz_questions.length > 0) {
        currentQuizData = {
            lesson_id: lessonData.lesson_id,
            subject: lessonData.subject,
            topic: lessonData.topic,
            questions: lessonData.quiz_questions
        };
        
        // Store questions on server for evaluation
        storeQuizQuestions(lessonData.lesson_id, lessonData.quiz_questions);
        
        setTimeout(() => {
            showQuiz(lessonData.quiz_questions);
        }, 2000);
    }
}

async function storeQuizQuestions(lessonId, questions) {
    try {
        await fetch(`${API_BASE_URL}/quiz/store-questions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                quiz_id: lessonId,
                questions: questions
            })
        });
    } catch (error) {
        console.error('Error storing quiz questions:', error);
    }
}

function showQuiz(questions) {
    const quizContainer = document.getElementById('quizContainer');
    const questionsContainer = document.getElementById('quizQuestions');
    
    questionsContainer.innerHTML = questions.map((question, index) => {
        if (question.type === 'multiple_choice' && question.options) {
            return `
                <div class="quiz-question">
                    <h4>Question ${index + 1}: ${question.question}</h4>
                    <div class="quiz-options">
                        ${question.options.map(option => `
                            <label class="quiz-option">
                                <input type="radio" name="question_${index}" value="${option}">
                                <span>${option}</span>
                            </label>
                        `).join('')}
                    </div>
                </div>
            `;
        } else {
            return `
                <div class="quiz-question">
                    <h4>Question ${index + 1}: ${question.question}</h4>
                    <input type="text" class="quiz-text-input" name="question_${index}" placeholder="Enter your answer">
                </div>
            `;
        }
    }).join('');
    
    quizContainer.style.display = 'block';
}

async function submitQuiz() {
    if (!currentQuizData) return;
    
    const answers = {};
    const questions = currentQuizData.questions;
    
    questions.forEach((question, index) => {
        const questionName = `question_${index}`;
        const input = document.querySelector(`[name="${questionName}"]`);
        
        if (input) {
            if (input.type === 'radio') {
                const checkedInput = document.querySelector(`[name="${questionName}"]:checked`);
                answers[`q${index + 1}`] = checkedInput ? checkedInput.value : '';
            } else {
                answers[`q${index + 1}`] = input.value;
            }
        }
    });
    
    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/quiz/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                quiz_id: currentQuizData.lesson_id,
                subject: currentQuizData.subject,
                topic: currentQuizData.topic,
                answers: answers
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            displayQuizResults(data);
        } else {
            showToast(data.detail || 'Failed to submit quiz', 'error');
        }
    } catch (error) {
        showToast('Network error. Please try again.', 'error');
    }
    
    showLoading(false);
}

function displayQuizResults(results) {
    document.getElementById('quizContainer').style.display = 'none';
    
    const resultsContainer = document.getElementById('quizResults');
    document.getElementById('quizScore').textContent = `${results.score}% (${results.correct_count}/${results.total_questions})`;
    document.getElementById('quizFeedback').textContent = results.feedback;
    
    resultsContainer.style.display = 'block';
    
    // Save progress
    saveProgress(currentQuizData.subject, currentQuizData.topic, results.score);
}

async function saveProgress(subject, topic, score) {
    try {
        await fetch(`${API_BASE_URL}/progress/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                subject,
                topic,
                score,
                lesson_content: currentLessonData?.content || ''
            })
        });
    } catch (error) {
        console.error('Error saving progress:', error);
    }
}

function continueLesson() {
    // For now, just close the lesson and refresh dashboard
    finishLesson();
}

function finishLesson() {
    closeModals();
    loadDashboardData(); // Refresh dashboard data
    showToast('Lesson completed! Great job!', 'success');
}

// Utility Functions
function closeModals() {
    authModal.style.display = 'none';
    lessonModal.style.display = 'none';
    
    // Reset quiz state
    document.getElementById('quizContainer').style.display = 'none';
    document.getElementById('quizResults').style.display = 'none';
    currentQuizData = null;
    currentLessonData = null;
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    toastContainer.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// Navigation smooth scrolling
document.querySelectorAll('.nav-link[href^="#"]').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
        }
    });
});
