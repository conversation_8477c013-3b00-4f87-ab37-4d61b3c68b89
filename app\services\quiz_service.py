from sqlalchemy.orm import Session
from app.db.models import QuizResult, User
from app.schemas.tutor import QuizSubmission, QuizEvaluation
from app.services.ai_service import AIService
import json
from typing import List, Dict, Any

class QuizService:
    def __init__(self):
        self.ai_service = AIService()
    
    async def submit_quiz(self, db: Session, user: User, submission: QuizSubmission, questions: List[Dict[str, Any]]) -> QuizResult:
        """Submit and evaluate a quiz."""
        try:
            # Evaluate quiz using AI
            evaluation = await self.ai_service.evaluate_quiz(
                subject=submission.subject,
                topic=submission.topic,
                questions=questions,
                user_answers=submission.answers
            )
            
            # Create quiz result record
            quiz_result = QuizResult(
                user_id=user.id,
                quiz_id=submission.quiz_id,
                subject=submission.subject,
                topic=submission.topic,
                answers=json.dumps(submission.answers),
                correct_answers=json.dumps({q["id"]: q["correct_answer"] for q in questions}),
                score=evaluation["score"],
                total_questions=evaluation["total_questions"],
                correct_count=evaluation["correct_count"],
                feedback=evaluation["feedback"]
            )
            
            db.add(quiz_result)
            db.commit()
            db.refresh(quiz_result)
            
            return quiz_result
            
        except Exception as e:
            db.rollback()
            raise e
    
    def get_quiz_results(self, db: Session, quiz_id: str, user_id: int) -> QuizResult:
        """Get quiz results by quiz ID and user ID."""
        return db.query(QuizResult).filter(
            QuizResult.quiz_id == quiz_id,
            QuizResult.user_id == user_id
        ).first()
    
    def get_user_quiz_history(self, db: Session, user_id: int, limit: int = 10) -> List[QuizResult]:
        """Get user's quiz history."""
        return db.query(QuizResult).filter(
            QuizResult.user_id == user_id
        ).order_by(QuizResult.created_at.desc()).limit(limit).all()
    
    def calculate_detailed_results(self, quiz_result: QuizResult, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate detailed results for each question."""
        try:
            user_answers = json.loads(quiz_result.answers)
            correct_answers = json.loads(quiz_result.correct_answers)
            
            detailed_results = []
            for question in questions:
                q_id = question["id"]
                user_answer = user_answers.get(q_id, "")
                correct_answer = correct_answers.get(q_id, "")
                is_correct = user_answer.lower().strip() == correct_answer.lower().strip()
                
                detailed_results.append({
                    "question_id": q_id,
                    "question": question["question"],
                    "user_answer": user_answer,
                    "correct_answer": correct_answer,
                    "is_correct": is_correct,
                    "explanation": f"The correct answer is: {correct_answer}"
                })
            
            return detailed_results
            
        except Exception as e:
            return []
