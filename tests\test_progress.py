import pytest
from fastapi.testclient import TestClient

def test_save_progress(authenticated_client: TestClient):
    """Test saving user progress."""
    progress_data = {
        "subject": "Mathematics",
        "topic": "Basic Arithmetic",
        "score": 85,
        "lesson_content": "Learned addition and subtraction"
    }
    
    response = authenticated_client.post("/api/v1/progress/save", json=progress_data)
    assert response.status_code == 201
    data = response.json()
    assert data["subject"] == progress_data["subject"]
    assert data["topic"] == progress_data["topic"]
    assert data["score"] == progress_data["score"]

def test_fetch_progress(authenticated_client: TestClient):
    """Test fetching user progress."""
    # Save some progress first
    progress_data = {
        "subject": "Science",
        "topic": "Physics Basics",
        "score": 90
    }
    authenticated_client.post("/api/v1/progress/save", json=progress_data)
    
    # Fetch progress
    response = authenticated_client.get("/api/v1/progress/fetch")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0
    assert data[0]["subject"] == progress_data["subject"]

def test_progress_summary(authenticated_client: TestClient):
    """Test getting progress summary."""
    # Save multiple progress records
    progress_records = [
        {"subject": "Math", "topic": "Addition", "score": 80},
        {"subject": "Math", "topic": "Subtraction", "score": 85},
        {"subject": "Science", "topic": "Physics", "score": 90}
    ]
    
    for progress in progress_records:
        authenticated_client.post("/api/v1/progress/save", json=progress)
    
    # Get summary
    response = authenticated_client.get("/api/v1/progress/summary")
    assert response.status_code == 200
    data = response.json()
    assert data["total_lessons"] == 3
    assert data["average_score"] > 0
    assert "Math" in data["subjects"]
    assert "Science" in data["subjects"]

def test_progress_statistics(authenticated_client: TestClient):
    """Test getting progress statistics."""
    # Save some progress
    progress_data = {"subject": "English", "topic": "Grammar", "score": 75}
    authenticated_client.post("/api/v1/progress/save", json=progress_data)
    
    response = authenticated_client.get("/api/v1/progress/statistics")
    assert response.status_code == 200
    data = response.json()
    assert "subject_statistics" in data
    assert "learning_streak" in data
