from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.core.deps import get_current_user
from app.db.models import User
from app.schemas.tutor import LessonStartRequest, LessonNextRequest, LessonResponse
from app.services.ai_service import AIService
import uuid

router = APIRouter()
ai_service = AIService()

@router.post("/lesson/start", response_model=LessonResponse)
async def start_lesson(
    request: LessonStartRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start a new lesson for the user."""
    try:
        # Use user's level if not specified in request
        user_level = request.user_level or current_user.level
        
        # Generate lesson content using AI
        lesson_data = await ai_service.generate_lesson(
            subject=request.subject,
            topic=request.topic,
            level=user_level
        )
        
        # Generate unique lesson ID
        lesson_id = str(uuid.uuid4())
        
        # Prepare response
        response = LessonResponse(
            content=lesson_data["content"],
            quiz_questions=lesson_data["quiz_questions"],
            lesson_id=lesson_id,
            subject=request.subject,
            topic=request.topic
        )
        
        return response
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start lesson: {str(e)}"
        )

@router.post("/lesson/next", response_model=LessonResponse)
async def next_lesson_content(
    request: LessonNextRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get next lesson content based on user interaction."""
    try:
        # Use user's level if not specified in request
        user_level = request.user_level or current_user.level
        
        # Generate continuation content using AI
        lesson_data = await ai_service.generate_lesson(
            subject=request.subject,
            topic=request.topic,
            level=user_level,
            previous_content=request.previous_content,
            user_response=request.user_response
        )
        
        # Generate unique lesson ID
        lesson_id = str(uuid.uuid4())
        
        # Prepare response
        response = LessonResponse(
            content=lesson_data["content"],
            quiz_questions=lesson_data["quiz_questions"],
            lesson_id=lesson_id,
            subject=request.subject,
            topic=request.topic
        )
        
        return response
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get next lesson content: {str(e)}"
        )
