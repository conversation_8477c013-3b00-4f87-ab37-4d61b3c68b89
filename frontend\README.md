# SkillCred Frontend

A modern, responsive web application for the Educational Tutor API.

## Features

- **Modern UI/UX**: Clean, professional design with smooth animations
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Real-time API Integration**: Seamless connection to the Educational Tutor API
- **Interactive Learning**: Step-by-step lessons with AI-powered content
- **Dynamic Quizzes**: Multiple choice and text-based questions with instant feedback
- **Progress Tracking**: Comprehensive dashboard with learning analytics
- **Smart Recommendations**: AI-powered topic suggestions based on performance

## Quick Start

1. **Make sure the API server is running**:
   ```bash
   # In the main project directory
   python run.py
   ```

2. **Start the frontend server**:
   ```bash
   # In the frontend directory
   python server.py
   ```

3. **Open your browser**:
   - The server will automatically open http://localhost:3000
   - If not, manually navigate to http://localhost:3000

## File Structure

```
frontend/
├── index.html          # Main HTML file
├── styles.css          # CSS styles and responsive design
├── script.js           # JavaScript functionality and API integration
├── server.py           # Simple HTTP server for development
└── README.md           # This file
```

## How to Use

### 1. Registration/Login
- Click "Get Started" or "Login" to open the authentication modal
- Register a new account or login with existing credentials
- Choose your learning level (Beginner, Intermediate, Advanced)

### 2. Dashboard
- View your learning statistics and progress
- See personalized recommendations
- Start new lessons by selecting subject and topic

### 3. Learning Experience
- Read AI-generated lesson content
- Take interactive quizzes
- Get instant feedback and explanations
- Track your progress automatically

### 4. Progress Tracking
- Monitor your learning streak
- View detailed statistics by subject
- See recent learning activities

## API Integration

The frontend connects to the Educational Tutor API running on `http://localhost:8000/api/v1`.

### Key API Endpoints Used:
- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication
- `POST /tutor/lesson/start` - Start new lessons
- `POST /quiz/submit` - Submit quiz answers
- `GET /progress/summary` - Get learning progress
- `GET /recommend/next` - Get topic recommendations

## Customization

### Styling
- Edit `styles.css` to customize colors, fonts, and layout
- The design uses CSS Grid and Flexbox for responsive layouts
- Color scheme is based on modern design principles

### Functionality
- Modify `script.js` to add new features or change behavior
- All API calls are centralized and easy to modify
- Event handlers are clearly organized

### Content
- Update `index.html` to change text content or structure
- Add new sections or modify existing ones
- All content is semantic and accessible

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Development Notes

### CORS Handling
The development server includes CORS headers to allow API communication.

### Local Storage
User authentication tokens and data are stored in browser's localStorage.

### Error Handling
Comprehensive error handling with user-friendly toast notifications.

### Loading States
Loading overlays and spinners provide feedback during API calls.

## Production Deployment

For production deployment:

1. **Build Process**: No build process required - pure HTML/CSS/JS
2. **Web Server**: Use any web server (Apache, Nginx, etc.)
3. **API Configuration**: Update `API_BASE_URL` in `script.js` to your production API URL
4. **HTTPS**: Ensure both frontend and API use HTTPS in production

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Ensure the API server is running on http://localhost:8000
   - Check browser console for CORS errors
   - Verify API endpoints are accessible

2. **Login Issues**
   - Clear browser localStorage and try again
   - Check API server logs for authentication errors
   - Verify user credentials

3. **Styling Issues**
   - Hard refresh the browser (Ctrl+F5)
   - Check browser developer tools for CSS errors
   - Ensure all CSS files are loading correctly

### Debug Mode
Open browser developer tools (F12) to see:
- Network requests to the API
- JavaScript console logs
- Local storage data

## Contributing

To contribute to the frontend:

1. Follow the existing code style and structure
2. Test on multiple browsers and devices
3. Ensure responsive design principles
4. Add comments for complex functionality

## License

This project is part of the SkillCred Educational Tutor system.
