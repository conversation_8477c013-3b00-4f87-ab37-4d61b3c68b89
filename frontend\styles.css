/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #4f46e5;
}

.nav-brand i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: color 0.3s;
    cursor: pointer;
}

.nav-link:hover,
.nav-link.active {
    color: #4f46e5;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background: #4338ca;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #4f46e5;
}

.hero-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    border: 1px solid rgba(255,255,255,0.2);
}

.hero-card i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #fbbf24;
}

.hero-card h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: white;
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #1e293b;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.feature-card {
    text-align: center;
    padding: 40px 20px;
    border-radius: 15px;
    background: #f8fafc;
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.feature-card i {
    font-size: 3rem;
    color: #4f46e5;
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #1e293b;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

.lesson-modal {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

@keyframes modalSlideIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #64748b;
    z-index: 10;
}

.close:hover {
    color: #ef4444;
}

/* Auth Forms */
.auth-container {
    padding: 40px;
}

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    flex: 1;
    padding: 15px;
    border: none;
    background: none;
    font-size: 1rem;
    font-weight: 600;
    color: #64748b;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.tab-btn.active {
    color: #4f46e5;
    border-bottom-color: #4f46e5;
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    margin-bottom: 30px;
    color: #1e293b;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #4f46e5;
}

.full-width {
    width: 100%;
}

/* Dashboard */
.dashboard {
    padding: 100px 0 50px;
    min-height: 100vh;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.dashboard-header h2 {
    color: #1e293b;
    font-size: 2rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.level-badge {
    background: #4f46e5;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card i {
    font-size: 2.5rem;
    color: #4f46e5;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 5px;
}

.stat-info p {
    color: #64748b;
    font-size: 0.9rem;
}

/* Dashboard Sections */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.dashboard-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.dashboard-section h3 {
    margin-bottom: 20px;
    color: #1e293b;
    font-size: 1.3rem;
}

.lesson-starter {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Recommendations */
.recommendations-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation-card {
    padding: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    transition: all 0.3s;
    cursor: pointer;
}

.recommendation-card:hover {
    border-color: #4f46e5;
    background: #f8fafc;
}

.recommendation-card h4 {
    color: #1e293b;
    margin-bottom: 5px;
}

.recommendation-card p {
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.recommendation-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #64748b;
}

.priority-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.priority-1 { background: #fee2e2; color: #dc2626; }
.priority-2 { background: #fef3c7; color: #d97706; }
.priority-3 { background: #dcfce7; color: #16a34a; }

/* Progress List */
.progress-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.progress-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8fafc;
    border-radius: 10px;
}

.progress-info h4 {
    color: #1e293b;
    margin-bottom: 5px;
}

.progress-info p {
    color: #64748b;
    font-size: 0.9rem;
}

.progress-score {
    font-size: 1.2rem;
    font-weight: 700;
    color: #4f46e5;
}

/* Lesson Modal */
.lesson-container {
    padding: 40px;
}

.lesson-header {
    margin-bottom: 30px;
    text-align: center;
}

.lesson-header h2 {
    color: #1e293b;
    margin-bottom: 10px;
}

.lesson-meta {
    color: #64748b;
    font-size: 0.9rem;
}

.lesson-text {
    background: #f8fafc;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    line-height: 1.8;
    color: #374151;
}

/* Quiz Styles */
.quiz-container {
    margin-top: 30px;
}

.quiz-question {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 10px;
}

.quiz-question h4 {
    margin-bottom: 15px;
    color: #1e293b;
}

.quiz-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.quiz-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

.quiz-option:hover {
    border-color: #4f46e5;
    background: white;
}

.quiz-option input[type="radio"] {
    margin: 0;
}

.quiz-text-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
}

.quiz-results {
    text-align: center;
    padding: 30px;
    background: #f8fafc;
    border-radius: 10px;
}

.quiz-score {
    font-size: 2rem;
    font-weight: 700;
    color: #4f46e5;
    margin-bottom: 20px;
}

.quiz-feedback {
    color: #374151;
    line-height: 1.6;
    margin-bottom: 30px;
}

/* Loading and Utilities */
.loading {
    text-align: center;
    color: #64748b;
    padding: 20px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 3000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 90px;
    right: 20px;
    z-index: 4000;
}

.toast {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    border-left: 4px solid #4f46e5;
    animation: toastSlideIn 0.3s ease;
}

.toast.error {
    border-left-color: #ef4444;
}

.toast.success {
    border-left-color: #10b981;
}

@keyframes toastSlideIn {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

/* Additional Styles */
.dashboard-section:nth-child(odd) {
    grid-column: 1 / -1;
}

.full-width-section {
    grid-column: 1 / -1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .lesson-container,
    .auth-container {
        padding: 20px;
    }
}
