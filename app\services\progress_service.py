from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from app.db.models import Progress, User
from app.schemas.progress import ProgressC<PERSON>, ProgressResponse, ProgressSummary
from typing import List, Dict, Any

class ProgressService:
    @staticmethod
    def save_progress(db: Session, user: User, progress_data: ProgressCreate) -> Progress:
        """Save user's learning progress."""
        try:
            progress = Progress(
                user_id=user.id,
                subject=progress_data.subject,
                topic=progress_data.topic,
                score=progress_data.score,
                lesson_content=progress_data.lesson_content
            )
            
            db.add(progress)
            db.commit()
            db.refresh(progress)
            
            return progress
            
        except Exception as e:
            db.rollback()
            raise e
    
    @staticmethod
    def get_user_progress(db: Session, user_id: int, limit: int = 50) -> List[Progress]:
        """Get user's progress records."""
        return db.query(Progress).filter(
            Progress.user_id == user_id
        ).order_by(desc(Progress.timestamp)).limit(limit).all()
    
    @staticmethod
    def get_progress_by_subject(db: Session, user_id: int, subject: str) -> List[Progress]:
        """Get user's progress for a specific subject."""
        return db.query(Progress).filter(
            Progress.user_id == user_id,
            Progress.subject == subject
        ).order_by(desc(Progress.timestamp)).all()
    
    @staticmethod
    def get_progress_summary(db: Session, user_id: int) -> ProgressSummary:
        """Get comprehensive progress summary for user."""
        try:
            # Get all progress records
            all_progress = db.query(Progress).filter(Progress.user_id == user_id).all()
            
            if not all_progress:
                return ProgressSummary(
                    user_id=user_id,
                    total_lessons=0,
                    average_score=0.0,
                    subjects=[],
                    recent_progress=[]
                )
            
            # Calculate statistics
            total_lessons = len(all_progress)
            average_score = sum(p.score for p in all_progress) / total_lessons
            subjects = list(set(p.subject for p in all_progress))
            
            # Get recent progress (last 10)
            recent_progress = db.query(Progress).filter(
                Progress.user_id == user_id
            ).order_by(desc(Progress.timestamp)).limit(10).all()
            
            return ProgressSummary(
                user_id=user_id,
                total_lessons=total_lessons,
                average_score=round(average_score, 2),
                subjects=subjects,
                recent_progress=[ProgressResponse.from_orm(p) for p in recent_progress]
            )
            
        except Exception as e:
            raise e
    
    @staticmethod
    def get_subject_statistics(db: Session, user_id: int) -> Dict[str, Any]:
        """Get detailed statistics by subject."""
        try:
            # Query progress grouped by subject
            subject_stats = db.query(
                Progress.subject,
                func.count(Progress.id).label('lesson_count'),
                func.avg(Progress.score).label('avg_score'),
                func.max(Progress.score).label('max_score'),
                func.min(Progress.score).label('min_score')
            ).filter(
                Progress.user_id == user_id
            ).group_by(Progress.subject).all()
            
            statistics = {}
            for stat in subject_stats:
                statistics[stat.subject] = {
                    'lesson_count': stat.lesson_count,
                    'average_score': round(stat.avg_score, 2),
                    'max_score': stat.max_score,
                    'min_score': stat.min_score
                }
            
            return statistics
            
        except Exception as e:
            return {}
    
    @staticmethod
    def get_learning_streak(db: Session, user_id: int) -> Dict[str, Any]:
        """Calculate user's learning streak."""
        try:
            # Get progress records ordered by date
            progress_records = db.query(Progress).filter(
                Progress.user_id == user_id
            ).order_by(desc(Progress.timestamp)).all()
            
            if not progress_records:
                return {"current_streak": 0, "longest_streak": 0, "last_activity": None}
            
            # Calculate streaks (simplified - assumes one lesson per day max)
            dates = [p.timestamp.date() for p in progress_records]
            unique_dates = sorted(list(set(dates)), reverse=True)
            
            current_streak = 0
            longest_streak = 0
            temp_streak = 1
            
            # Calculate current streak
            from datetime import date, timedelta
            today = date.today()
            
            if unique_dates and unique_dates[0] >= today - timedelta(days=1):
                current_streak = 1
                for i in range(1, len(unique_dates)):
                    if unique_dates[i-1] - unique_dates[i] == timedelta(days=1):
                        current_streak += 1
                    else:
                        break
            
            # Calculate longest streak
            for i in range(1, len(unique_dates)):
                if unique_dates[i-1] - unique_dates[i] == timedelta(days=1):
                    temp_streak += 1
                    longest_streak = max(longest_streak, temp_streak)
                else:
                    temp_streak = 1
            
            longest_streak = max(longest_streak, temp_streak)
            
            return {
                "current_streak": current_streak,
                "longest_streak": longest_streak,
                "last_activity": progress_records[0].timestamp if progress_records else None
            }
            
        except Exception as e:
            return {"current_streak": 0, "longest_streak": 0, "last_activity": None}
