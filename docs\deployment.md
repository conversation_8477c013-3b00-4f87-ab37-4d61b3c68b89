# Deployment Guide

This guide covers deploying the Educational Tutor API to various cloud platforms.

## Environment Setup

### Required Environment Variables

Create a `.env` file with the following variables:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# JWT Configuration  
SECRET_KEY=your_very_secure_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Configuration
DATABASE_URL=sqlite:///./tutor_app.db

# App Configuration
APP_NAME=Educational Tutor API
DEBUG=False
```

## Deployment Options

### 1. Render (Recommended)

Render provides easy deployment with automatic builds and SSL certificates.

#### Steps:
1. Push your code to GitHub
2. Connect your GitHub repo to Render
3. Create a new Web Service
4. Configure build and start commands:
   - **Build Command:** `pip install -r requirements.txt`
   - **Start Command:** `uvicorn app.main:app --host 0.0.0.0 --port $PORT`
5. Add environment variables in Render dashboard
6. Deploy!

#### Render Configuration File (`render.yaml`):
```yaml
services:
  - type: web
    name: educational-tutor-api
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn app.main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: OPENAI_API_KEY
        sync: false
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        value: sqlite:///./tutor_app.db
```

### 2. Railway

Railway offers simple deployment with built-in database options.

#### Steps:
1. Install Railway CLI: `npm install -g @railway/cli`
2. Login: `railway login`
3. Initialize project: `railway init`
4. Add environment variables: `railway variables set OPENAI_API_KEY=your_key`
5. Deploy: `railway up`

### 3. Heroku

#### Steps:
1. Install Heroku CLI
2. Create Heroku app: `heroku create your-app-name`
3. Add environment variables: `heroku config:set OPENAI_API_KEY=your_key`
4. Create `Procfile`:
   ```
   web: uvicorn app.main:app --host 0.0.0.0 --port $PORT
   ```
5. Deploy: `git push heroku main`

### 4. AWS/GCP/Azure

For cloud platforms, consider using:
- **AWS:** Elastic Beanstalk or ECS
- **GCP:** App Engine or Cloud Run  
- **Azure:** App Service or Container Instances

## Database Considerations

### SQLite (Development/Small Scale)
- Included by default
- File-based database
- Good for prototyping and small applications

### PostgreSQL (Production Recommended)
- Better for production workloads
- Supports concurrent users
- Available on most cloud platforms

To use PostgreSQL:
1. Update `requirements.txt`:
   ```
   psycopg2-binary==2.9.7
   ```
2. Update `DATABASE_URL`:
   ```
   DATABASE_URL=postgresql://user:password@host:port/database
   ```

## Performance Optimization

### 1. Caching
Consider adding Redis for caching:
```python
# Add to requirements.txt
redis==4.5.4
```

### 2. Database Connection Pooling
For production, use connection pooling:
```python
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=0
)
```

### 3. Async Database Operations
Consider using async database operations for better performance:
```python
# Add to requirements.txt
asyncpg==0.28.0
databases==0.7.0
```

## Security Checklist

- [ ] Use strong SECRET_KEY (generate with `openssl rand -hex 32`)
- [ ] Set DEBUG=False in production
- [ ] Configure CORS properly for your frontend domains
- [ ] Use HTTPS (most platforms provide this automatically)
- [ ] Validate and sanitize all inputs
- [ ] Implement rate limiting
- [ ] Monitor API usage and errors
- [ ] Keep dependencies updated

## Monitoring and Logging

### Basic Logging Setup
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
```

### Health Checks
The API includes health check endpoints:
- `/health` - Basic health check
- `/` - API information

### Monitoring Services
Consider integrating with:
- **Sentry** for error tracking
- **DataDog** for performance monitoring
- **New Relic** for application monitoring

## Scaling Considerations

### Horizontal Scaling
- Use load balancers
- Implement stateless design
- Consider microservices architecture

### Database Scaling
- Read replicas for read-heavy workloads
- Database sharding for large datasets
- Consider managed database services

### Caching Strategy
- Redis for session storage
- CDN for static content
- Application-level caching for API responses

## Backup and Recovery

### Database Backups
- Automated daily backups
- Point-in-time recovery
- Test backup restoration regularly

### Code Backups
- Use version control (Git)
- Multiple repository mirrors
- Automated deployment pipelines

## Cost Optimization

### OpenAI API Costs
- Monitor token usage
- Implement request caching
- Use appropriate model sizes
- Set usage limits

### Infrastructure Costs
- Right-size your instances
- Use auto-scaling
- Monitor resource usage
- Consider spot instances for non-critical workloads

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL format
   - Verify database server is running
   - Check network connectivity

2. **OpenAI API Errors**
   - Verify API key is correct
   - Check rate limits
   - Monitor API quotas

3. **Authentication Issues**
   - Verify SECRET_KEY is set
   - Check token expiration
   - Validate JWT format

### Debugging Tools
- Use FastAPI's automatic documentation at `/docs`
- Enable debug logging in development
- Use API testing tools like Postman or curl

## Support and Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor error rates
- Review performance metrics
- Update documentation

### Security Updates
- Subscribe to security advisories
- Update dependencies promptly
- Regular security audits
- Penetration testing
