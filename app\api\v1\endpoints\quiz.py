from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.core.deps import get_current_user
from app.db.models import User
from app.schemas.tutor import QuizSubmission, QuizEvaluation
from app.services.quiz_service import QuizService
from typing import List, Dict, Any

router = APIRouter()
quiz_service = QuizService()

# In-memory storage for quiz questions (in production, use Redis or database)
quiz_questions_store: Dict[str, List[Dict[str, Any]]] = {}

@router.post("/submit", response_model=QuizEvaluation)
async def submit_quiz(
    submission: QuizSubmission,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Submit quiz answers for evaluation."""
    try:
        # Get quiz questions from store
        questions = quiz_questions_store.get(submission.quiz_id, [])
        if not questions:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Quiz questions not found. Please start a new lesson."
            )
        
        # Submit and evaluate quiz
        quiz_result = await quiz_service.submit_quiz(db, current_user, submission, questions)
        
        # Calculate detailed results
        detailed_results = quiz_service.calculate_detailed_results(quiz_result, questions)
        
        # Prepare evaluation response
        evaluation = QuizEvaluation(
            score=quiz_result.score,
            total_questions=quiz_result.total_questions,
            correct_count=quiz_result.correct_count,
            feedback=quiz_result.feedback,
            strengths=["Good effort on the quiz"],  # Could be enhanced with AI analysis
            weaknesses=["Review incorrect answers"],  # Could be enhanced with AI analysis
            detailed_results=detailed_results
        )
        
        return evaluation
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit quiz: {str(e)}"
        )

@router.get("/results/{quiz_id}", response_model=QuizEvaluation)
async def get_quiz_results(
    quiz_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get quiz results by quiz ID."""
    try:
        # Get quiz result from database
        quiz_result = quiz_service.get_quiz_results(db, quiz_id, current_user.id)
        if not quiz_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Quiz results not found"
            )
        
        # Get quiz questions from store
        questions = quiz_questions_store.get(quiz_id, [])
        
        # Calculate detailed results
        detailed_results = quiz_service.calculate_detailed_results(quiz_result, questions)
        
        # Prepare evaluation response
        evaluation = QuizEvaluation(
            score=quiz_result.score,
            total_questions=quiz_result.total_questions,
            correct_count=quiz_result.correct_count,
            feedback=quiz_result.feedback,
            strengths=["Quiz completed"],
            weaknesses=["Review material"],
            detailed_results=detailed_results
        )
        
        return evaluation
        
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get quiz results: {str(e)}"
        )

@router.get("/history")
async def get_quiz_history(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's quiz history."""
    try:
        quiz_history = quiz_service.get_user_quiz_history(db, current_user.id)
        
        history_data = []
        for quiz in quiz_history:
            history_data.append({
                "quiz_id": quiz.quiz_id,
                "subject": quiz.subject,
                "topic": quiz.topic,
                "score": quiz.score,
                "total_questions": quiz.total_questions,
                "correct_count": quiz.correct_count,
                "created_at": quiz.created_at
            })
        
        return {"quiz_history": history_data}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get quiz history: {str(e)}"
        )

# Helper endpoint to store quiz questions (called internally)
@router.post("/store-questions")
async def store_quiz_questions(
    quiz_id: str,
    questions: List[Dict[str, Any]],
    current_user: User = Depends(get_current_user)
):
    """Store quiz questions for later evaluation."""
    quiz_questions_store[quiz_id] = questions
    return {"message": "Questions stored successfully"}
