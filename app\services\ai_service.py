from openai import OpenAI
import json
from typing import Dict, List, Any
from app.core.config import settings
from app.services.demo_content import get_demo_lesson
from fastapi import HTT<PERSON>Ex<PERSON>, status

class AIService:
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
    
    async def generate_lesson(self, subject: str, topic: str, level: str, previous_content: str = None, user_response: str = None) -> Dict[str, Any]:
        """Generate lesson content using GPT."""
        try:
            # If no OpenAI client, return demo content
            if not self.client:
                return get_demo_lesson(subject, topic, level)

            # Build the prompt based on whether this is a new lesson or continuation
            if previous_content and user_response:
                prompt = self._build_continuation_prompt(subject, topic, level, previous_content, user_response)
            else:
                prompt = self._build_lesson_prompt(subject, topic, level)

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert educational tutor. Provide clear, engaging, and age-appropriate lessons."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )

            content = response.choices[0].message.content

            # Parse the response to extract lesson content and quiz questions
            return self._parse_lesson_response(content, subject, topic)

        except Exception as e:
            # Fallback to demo content if API fails
            return get_demo_lesson(subject, topic, level)
    
    async def evaluate_quiz(self, subject: str, topic: str, questions: List[Dict], user_answers: Dict[str, str]) -> Dict[str, Any]:
        """Evaluate quiz answers using GPT."""
        try:
            # If no OpenAI client, return mock evaluation
            if not self.client:
                return self._generate_mock_evaluation(questions, user_answers)

            prompt = self._build_quiz_evaluation_prompt(subject, topic, questions, user_answers)

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert educational evaluator. Provide detailed, constructive feedback."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )

            content = response.choices[0].message.content
            return self._parse_evaluation_response(content, len(questions))

        except Exception as e:
            # Fallback to mock evaluation if API fails
            return self._generate_mock_evaluation(questions, user_answers)
    
    async def generate_recommendation(self, user_progress: List[Dict], current_level: str) -> Dict[str, Any]:
        """Generate next topic recommendation using GPT."""
        try:
            # If no OpenAI client, return mock recommendation
            if not self.client:
                return self._generate_mock_recommendation(user_progress, current_level)

            prompt = self._build_recommendation_prompt(user_progress, current_level)

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert educational advisor. Recommend the most appropriate next learning topic."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.5
            )

            content = response.choices[0].message.content
            return self._parse_recommendation_response(content)

        except Exception as e:
            # Fallback to mock recommendation if API fails
            return self._generate_mock_recommendation(user_progress, current_level)
    
    def _build_lesson_prompt(self, subject: str, topic: str, level: str) -> str:
        """Build prompt for new lesson generation."""
        return f"""
        You are a tutor teaching {subject}. Current topic: {topic}. The student level: {level}.
        
        Teach this topic step-by-step with clear explanations and examples. Structure your response as follows:
        
        LESSON CONTENT:
        [Provide detailed explanation with examples]
        
        QUIZ QUESTIONS:
        [Provide 3 questions in this exact format:]
        Q1: [Question text]
        Type: multiple_choice OR short_answer
        Options: [For multiple choice only: A) option1, B) option2, C) option3, D) option4]
        Answer: [Correct answer]
        
        Q2: [Question text]
        Type: multiple_choice OR short_answer
        Options: [If applicable]
        Answer: [Correct answer]
        
        Q3: [Question text]
        Type: multiple_choice OR short_answer
        Options: [If applicable]
        Answer: [Correct answer]
        
        Make the content engaging and appropriate for {level} level students.
        """
    
    def _build_continuation_prompt(self, subject: str, topic: str, level: str, previous_content: str, user_response: str) -> str:
        """Build prompt for lesson continuation."""
        return f"""
        You are continuing a {subject} lesson on {topic} for a {level} level student.
        
        Previous lesson content: {previous_content}
        Student's response/question: {user_response}
        
        Continue the lesson by:
        1. Addressing the student's response or question
        2. Building upon the previous content
        3. Providing additional explanations or examples as needed
        
        Structure your response as:
        
        LESSON CONTENT:
        [Continue the lesson based on student's response]
        
        QUIZ QUESTIONS:
        [Provide 2-3 new questions following the same format as before]
        """
    
    def _build_quiz_evaluation_prompt(self, subject: str, topic: str, questions: List[Dict], user_answers: Dict[str, str]) -> str:
        """Build prompt for quiz evaluation."""
        questions_text = ""
        for i, q in enumerate(questions, 1):
            questions_text += f"Q{i}: {q.get('question', '')}\n"
            if q.get('options'):
                questions_text += f"Options: {', '.join(q['options'])}\n"
            questions_text += f"Correct Answer: {q.get('correct_answer', '')}\n"
            questions_text += f"Student Answer: {user_answers.get(f'q{i}', 'No answer')}\n\n"
        
        return f"""
        Evaluate the student's quiz answers for {subject} - {topic}.
        
        {questions_text}
        
        Provide evaluation in this format:
        
        SCORE: [X out of {len(questions)}]
        PERCENTAGE: [percentage score]
        
        STRENGTHS:
        - [List what the student did well]
        
        WEAKNESSES:
        - [List areas for improvement]
        
        FEEDBACK:
        [Detailed constructive feedback for each question]
        
        OVERALL COMMENT:
        [Encouraging overall assessment]
        """
    
    def _build_recommendation_prompt(self, user_progress: List[Dict], current_level: str) -> str:
        """Build prompt for topic recommendation."""
        progress_text = ""
        for p in user_progress[-10:]:  # Last 10 progress records
            progress_text += f"Subject: {p.get('subject', '')}, Topic: {p.get('topic', '')}, Score: {p.get('score', 0)}\n"
        
        return f"""
        Based on the student's learning progress and current level ({current_level}), recommend the most appropriate next topic.
        
        Recent Progress:
        {progress_text}
        
        Provide recommendation in this format:
        
        RECOMMENDED TOPIC: [specific topic name]
        SUBJECT: [subject area]
        REASON: [explanation for why this topic is recommended]
        PRIORITY: [1 for high priority, 2 for medium, 3 for low]
        """
    
    def _parse_lesson_response(self, content: str, subject: str, topic: str) -> Dict[str, Any]:
        """Parse GPT lesson response into structured format."""
        try:
            # Split content into lesson and quiz sections
            parts = content.split("QUIZ QUESTIONS:")
            lesson_content = parts[0].replace("LESSON CONTENT:", "").strip()
            
            quiz_questions = []
            if len(parts) > 1:
                quiz_section = parts[1].strip()
                # Parse quiz questions (simplified parsing)
                questions = quiz_section.split("Q")[1:]  # Skip empty first element
                
                for i, q_text in enumerate(questions):
                    if q_text.strip():
                        lines = q_text.strip().split('\n')
                        question_text = lines[0].split(':', 1)[1].strip() if ':' in lines[0] else lines[0].strip()
                        
                        question = {
                            "id": f"q{i+1}",
                            "question": question_text,
                            "type": "short_answer",  # Default
                            "options": None,
                            "correct_answer": ""
                        }
                        
                        for line in lines[1:]:
                            if line.startswith("Type:"):
                                question["type"] = line.split(":", 1)[1].strip()
                            elif line.startswith("Options:"):
                                options_text = line.split(":", 1)[1].strip()
                                question["options"] = [opt.strip() for opt in options_text.split(",")]
                            elif line.startswith("Answer:"):
                                question["correct_answer"] = line.split(":", 1)[1].strip()
                        
                        quiz_questions.append(question)
            
            return {
                "content": lesson_content,
                "quiz_questions": quiz_questions,
                "subject": subject,
                "topic": topic
            }
            
        except Exception as e:
            # Fallback parsing
            return {
                "content": content,
                "quiz_questions": [],
                "subject": subject,
                "topic": topic
            }
    
    def _parse_evaluation_response(self, content: str, total_questions: int) -> Dict[str, Any]:
        """Parse GPT evaluation response into structured format."""
        try:
            lines = content.split('\n')
            score = 0
            percentage = 0
            strengths = []
            weaknesses = []
            feedback = ""
            
            current_section = None
            for line in lines:
                line = line.strip()
                if line.startswith("SCORE:"):
                    score_text = line.split(":", 1)[1].strip()
                    score = int(score_text.split()[0]) if score_text.split() else 0
                elif line.startswith("PERCENTAGE:"):
                    percentage_text = line.split(":", 1)[1].strip()
                    percentage = int(percentage_text.replace("%", "")) if percentage_text else 0
                elif line.startswith("STRENGTHS:"):
                    current_section = "strengths"
                elif line.startswith("WEAKNESSES:"):
                    current_section = "weaknesses"
                elif line.startswith("FEEDBACK:") or line.startswith("OVERALL COMMENT:"):
                    current_section = "feedback"
                elif line.startswith("- ") and current_section == "strengths":
                    strengths.append(line[2:])
                elif line.startswith("- ") and current_section == "weaknesses":
                    weaknesses.append(line[2:])
                elif current_section == "feedback" and line:
                    feedback += line + " "
            
            return {
                "score": percentage,
                "total_questions": total_questions,
                "correct_count": score,
                "feedback": feedback.strip(),
                "strengths": strengths,
                "weaknesses": weaknesses
            }
            
        except Exception as e:
            # Fallback evaluation
            return {
                "score": 50,
                "total_questions": total_questions,
                "correct_count": total_questions // 2,
                "feedback": "Quiz completed. Keep practicing!",
                "strengths": ["Attempted all questions"],
                "weaknesses": ["Review the material"]
            }
    
    def _parse_recommendation_response(self, content: str) -> Dict[str, Any]:
        """Parse GPT recommendation response into structured format."""
        try:
            lines = content.split('\n')
            recommendation = {
                "next_topic": "",
                "subject": "",
                "reason": "",
                "priority": 1
            }
            
            for line in lines:
                line = line.strip()
                if line.startswith("RECOMMENDED TOPIC:"):
                    recommendation["next_topic"] = line.split(":", 1)[1].strip()
                elif line.startswith("SUBJECT:"):
                    recommendation["subject"] = line.split(":", 1)[1].strip()
                elif line.startswith("REASON:"):
                    recommendation["reason"] = line.split(":", 1)[1].strip()
                elif line.startswith("PRIORITY:"):
                    priority_text = line.split(":", 1)[1].strip()
                    recommendation["priority"] = int(priority_text[0]) if priority_text and priority_text[0].isdigit() else 1
            
            return recommendation
            
        except Exception as e:
            # Fallback recommendation
            return {
                "next_topic": "Review fundamentals",
                "subject": "General",
                "reason": "Continue building foundational knowledge",
                "priority": 1
            }

    def _generate_mock_lesson(self, subject: str, topic: str, level: str) -> Dict[str, Any]:
        """Generate mock lesson content when OpenAI API is not available."""
        mock_lessons = {
            "Mathematics": {
                "Basic Arithmetic": {
                    "content": f"""
Welcome to {topic} in {subject}!

Let's start with the fundamentals of arithmetic operations. Arithmetic is the foundation of all mathematics, and mastering these basic operations will help you succeed in more advanced topics.

**Addition (+)**
Addition means combining numbers to get a total. For example:
- 3 + 2 = 5
- 10 + 7 = 17

**Subtraction (-)**
Subtraction means taking away one number from another. For example:
- 8 - 3 = 5
- 15 - 6 = 9

**Multiplication (×)**
Multiplication is repeated addition. For example:
- 4 × 3 = 12 (which is the same as 4 + 4 + 4)
- 6 × 2 = 12

**Division (÷)**
Division is splitting a number into equal parts. For example:
- 12 ÷ 3 = 4
- 20 ÷ 5 = 4

Practice these operations regularly to build your mathematical foundation!
                    """,
                    "questions": [
                        {
                            "id": "q1",
                            "question": "What is 15 + 8?",
                            "type": "multiple_choice",
                            "options": ["A) 21", "B) 23", "C) 25", "D) 27"],
                            "correct_answer": "B) 23"
                        },
                        {
                            "id": "q2",
                            "question": "What is 24 ÷ 6?",
                            "type": "multiple_choice",
                            "options": ["A) 3", "B) 4", "C) 5", "D) 6"],
                            "correct_answer": "B) 4"
                        },
                        {
                            "id": "q3",
                            "question": "What is 7 × 8?",
                            "type": "short_answer",
                            "correct_answer": "56"
                        }
                    ]
                },
                "Algebra": {
                    "content": f"""
Welcome to {topic} in {subject}!

Algebra is a branch of mathematics that uses letters and symbols to represent numbers and quantities in formulas and equations.

**Variables**
A variable is a letter (like x, y, or z) that represents an unknown number. For example:
- In the equation x + 3 = 7, x is the variable
- We can solve for x: x = 7 - 3 = 4

**Basic Algebraic Operations**
- Addition: x + 5
- Subtraction: x - 3
- Multiplication: 2x (means 2 times x)
- Division: x/4 (means x divided by 4)

**Solving Simple Equations**
To solve x + 5 = 12:
1. Subtract 5 from both sides
2. x + 5 - 5 = 12 - 5
3. x = 7

Let's practice with some examples!
                    """,
                    "questions": [
                        {
                            "id": "q1",
                            "question": "If x + 4 = 10, what is x?",
                            "type": "multiple_choice",
                            "options": ["A) 4", "B) 6", "C) 8", "D) 14"],
                            "correct_answer": "B) 6"
                        },
                        {
                            "id": "q2",
                            "question": "What is 3x when x = 5?",
                            "type": "short_answer",
                            "correct_answer": "15"
                        }
                    ]
                }
            },
            "Science": {
                "Physics Basics": {
                    "content": f"""
Welcome to {topic} in {subject}!

Physics is the study of matter, energy, and the interactions between them. Let's explore some fundamental concepts.

**Matter**
Everything around us is made of matter. Matter has:
- Mass (how much stuff is in an object)
- Volume (how much space it takes up)

**Energy**
Energy is the ability to do work or cause change. Types of energy include:
- Kinetic energy (energy of motion)
- Potential energy (stored energy)
- Heat energy
- Light energy

**Forces**
A force is a push or pull that can change an object's motion. Examples:
- Gravity pulls objects toward Earth
- Friction slows down moving objects
- Magnetic force attracts or repels magnets

**Motion**
Motion is when an object changes position over time. We can describe motion using:
- Speed (how fast something moves)
- Direction (which way it's going)
- Acceleration (change in speed)

Understanding these basics will help you explore more complex physics concepts!
                    """,
                    "questions": [
                        {
                            "id": "q1",
                            "question": "What type of energy does a moving car have?",
                            "type": "multiple_choice",
                            "options": ["A) Potential energy", "B) Kinetic energy", "C) Heat energy", "D) Light energy"],
                            "correct_answer": "B) Kinetic energy"
                        },
                        {
                            "id": "q2",
                            "question": "What force pulls objects toward Earth?",
                            "type": "short_answer",
                            "correct_answer": "Gravity"
                        }
                    ]
                }
            }
        }

        # Get lesson content or create generic one
        if subject in mock_lessons and topic in mock_lessons[subject]:
            lesson_data = mock_lessons[subject][topic]
        else:
            lesson_data = {
                "content": f"""
Welcome to your lesson on {topic} in {subject}!

This is an introductory lesson designed for {level} level students. We'll cover the fundamental concepts and provide practical examples to help you understand this topic.

Key Learning Objectives:
1. Understand the basic concepts of {topic}
2. Learn practical applications
3. Practice with examples
4. Test your knowledge with quizzes

Let's begin exploring {topic} together. This subject is fascinating and will provide you with valuable knowledge and skills.

Remember: Learning is a journey, and every expert was once a beginner. Take your time to understand each concept, and don't hesitate to review material as needed.

Ready to start learning? Let's dive in!
                """,
                "questions": [
                    {
                        "id": "q1",
                        "question": f"What is the main focus of studying {topic}?",
                        "type": "short_answer",
                        "correct_answer": f"Understanding the fundamental concepts of {topic}"
                    },
                    {
                        "id": "q2",
                        "question": f"Why is {topic} important in {subject}?",
                        "type": "short_answer",
                        "correct_answer": f"It provides foundational knowledge for {subject}"
                    }
                ]
            }

        return {
            "content": lesson_data["content"].strip(),
            "quiz_questions": lesson_data["questions"],
            "subject": subject,
            "topic": topic
        }

    def _generate_mock_evaluation(self, questions: List[Dict], user_answers: Dict[str, str]) -> Dict[str, Any]:
        """Generate mock quiz evaluation when OpenAI API is not available."""
        correct_count = 0
        total_questions = len(questions)

        for i, question in enumerate(questions):
            user_answer = user_answers.get(f"q{i+1}", "").strip().lower()
            correct_answer = question.get("correct_answer", "").strip().lower()

            if user_answer == correct_answer:
                correct_count += 1

        score = int((correct_count / total_questions) * 100) if total_questions > 0 else 0

        if score >= 80:
            feedback = "Excellent work! You have a strong understanding of this topic."
        elif score >= 60:
            feedback = "Good job! You're on the right track. Review the areas you missed."
        else:
            feedback = "Keep practicing! Review the lesson content and try again."

        return {
            "score": score,
            "total_questions": total_questions,
            "correct_count": correct_count,
            "feedback": feedback,
            "strengths": ["Good effort on the quiz"],
            "weaknesses": ["Review any incorrect answers"]
        }

    def _generate_mock_recommendation(self, user_progress: List[Dict], current_level: str) -> Dict[str, Any]:
        """Generate mock recommendation when OpenAI API is not available."""
        recommendations = {
            "beginner": [
                {"topic": "Basic Arithmetic", "subject": "Mathematics", "reason": "Build fundamental math skills"},
                {"topic": "Introduction to Science", "subject": "Science", "reason": "Explore basic scientific concepts"},
                {"topic": "Reading Comprehension", "subject": "English", "reason": "Improve reading and understanding skills"}
            ],
            "intermediate": [
                {"topic": "Algebra Basics", "subject": "Mathematics", "reason": "Advance your mathematical knowledge"},
                {"topic": "Physics Fundamentals", "subject": "Science", "reason": "Learn about forces and motion"},
                {"topic": "Essay Writing", "subject": "English", "reason": "Develop writing and communication skills"}
            ],
            "advanced": [
                {"topic": "Calculus Introduction", "subject": "Mathematics", "reason": "Explore advanced mathematical concepts"},
                {"topic": "Chemistry Principles", "subject": "Science", "reason": "Understand chemical reactions and properties"},
                {"topic": "Literature Analysis", "subject": "English", "reason": "Analyze complex texts and themes"}
            ]
        }

        level_recommendations = recommendations.get(current_level, recommendations["beginner"])

        # If user has progress, try to suggest next logical step
        if user_progress:
            last_subject = user_progress[0].get("subject", "Mathematics")
            avg_score = sum(p.get("score", 0) for p in user_progress[:5]) / min(len(user_progress), 5)

            if avg_score >= 80:
                return {
                    "next_topic": f"Advanced {last_subject} Concepts",
                    "subject": last_subject,
                    "reason": f"Your strong performance ({avg_score:.1f}% average) shows you're ready for more challenging topics",
                    "priority": 1
                }
            elif avg_score < 60:
                return {
                    "next_topic": f"Review {last_subject} Fundamentals",
                    "subject": last_subject,
                    "reason": f"Reviewing basics will strengthen your foundation (current average: {avg_score:.1f}%)",
                    "priority": 1
                }

        # Default recommendation
        import random
        rec = random.choice(level_recommendations)
        return {
            "next_topic": rec["topic"],
            "subject": rec["subject"],
            "reason": rec["reason"],
            "priority": 1
        }
