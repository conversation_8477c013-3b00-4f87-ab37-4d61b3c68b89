import openai
import j<PERSON>
from typing import Dict, List, Any
from app.core.config import settings
from fastapi import HTTPException, status

class AIService:
    def __init__(self):
        openai.api_key = settings.OPENAI_API_KEY
    
    async def generate_lesson(self, subject: str, topic: str, level: str, previous_content: str = None, user_response: str = None) -> Dict[str, Any]:
        """Generate lesson content using GPT."""
        try:
            # Build the prompt based on whether this is a new lesson or continuation
            if previous_content and user_response:
                prompt = self._build_continuation_prompt(subject, topic, level, previous_content, user_response)
            else:
                prompt = self._build_lesson_prompt(subject, topic, level)
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert educational tutor. Provide clear, engaging, and age-appropriate lessons."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            
            # Parse the response to extract lesson content and quiz questions
            return self._parse_lesson_response(content, subject, topic)
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate lesson: {str(e)}"
            )
    
    async def evaluate_quiz(self, subject: str, topic: str, questions: List[Dict], user_answers: Dict[str, str]) -> Dict[str, Any]:
        """Evaluate quiz answers using GPT."""
        try:
            prompt = self._build_quiz_evaluation_prompt(subject, topic, questions, user_answers)
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert educational evaluator. Provide detailed, constructive feedback."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )
            
            content = response.choices[0].message.content
            return self._parse_evaluation_response(content, len(questions))
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to evaluate quiz: {str(e)}"
            )
    
    async def generate_recommendation(self, user_progress: List[Dict], current_level: str) -> Dict[str, Any]:
        """Generate next topic recommendation using GPT."""
        try:
            prompt = self._build_recommendation_prompt(user_progress, current_level)
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert educational advisor. Recommend the most appropriate next learning topic."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.5
            )
            
            content = response.choices[0].message.content
            return self._parse_recommendation_response(content)
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate recommendation: {str(e)}"
            )
    
    def _build_lesson_prompt(self, subject: str, topic: str, level: str) -> str:
        """Build prompt for new lesson generation."""
        return f"""
        You are a tutor teaching {subject}. Current topic: {topic}. The student level: {level}.
        
        Teach this topic step-by-step with clear explanations and examples. Structure your response as follows:
        
        LESSON CONTENT:
        [Provide detailed explanation with examples]
        
        QUIZ QUESTIONS:
        [Provide 3 questions in this exact format:]
        Q1: [Question text]
        Type: multiple_choice OR short_answer
        Options: [For multiple choice only: A) option1, B) option2, C) option3, D) option4]
        Answer: [Correct answer]
        
        Q2: [Question text]
        Type: multiple_choice OR short_answer
        Options: [If applicable]
        Answer: [Correct answer]
        
        Q3: [Question text]
        Type: multiple_choice OR short_answer
        Options: [If applicable]
        Answer: [Correct answer]
        
        Make the content engaging and appropriate for {level} level students.
        """
    
    def _build_continuation_prompt(self, subject: str, topic: str, level: str, previous_content: str, user_response: str) -> str:
        """Build prompt for lesson continuation."""
        return f"""
        You are continuing a {subject} lesson on {topic} for a {level} level student.
        
        Previous lesson content: {previous_content}
        Student's response/question: {user_response}
        
        Continue the lesson by:
        1. Addressing the student's response or question
        2. Building upon the previous content
        3. Providing additional explanations or examples as needed
        
        Structure your response as:
        
        LESSON CONTENT:
        [Continue the lesson based on student's response]
        
        QUIZ QUESTIONS:
        [Provide 2-3 new questions following the same format as before]
        """
    
    def _build_quiz_evaluation_prompt(self, subject: str, topic: str, questions: List[Dict], user_answers: Dict[str, str]) -> str:
        """Build prompt for quiz evaluation."""
        questions_text = ""
        for i, q in enumerate(questions, 1):
            questions_text += f"Q{i}: {q.get('question', '')}\n"
            if q.get('options'):
                questions_text += f"Options: {', '.join(q['options'])}\n"
            questions_text += f"Correct Answer: {q.get('correct_answer', '')}\n"
            questions_text += f"Student Answer: {user_answers.get(f'q{i}', 'No answer')}\n\n"
        
        return f"""
        Evaluate the student's quiz answers for {subject} - {topic}.
        
        {questions_text}
        
        Provide evaluation in this format:
        
        SCORE: [X out of {len(questions)}]
        PERCENTAGE: [percentage score]
        
        STRENGTHS:
        - [List what the student did well]
        
        WEAKNESSES:
        - [List areas for improvement]
        
        FEEDBACK:
        [Detailed constructive feedback for each question]
        
        OVERALL COMMENT:
        [Encouraging overall assessment]
        """
    
    def _build_recommendation_prompt(self, user_progress: List[Dict], current_level: str) -> str:
        """Build prompt for topic recommendation."""
        progress_text = ""
        for p in user_progress[-10:]:  # Last 10 progress records
            progress_text += f"Subject: {p.get('subject', '')}, Topic: {p.get('topic', '')}, Score: {p.get('score', 0)}\n"
        
        return f"""
        Based on the student's learning progress and current level ({current_level}), recommend the most appropriate next topic.
        
        Recent Progress:
        {progress_text}
        
        Provide recommendation in this format:
        
        RECOMMENDED TOPIC: [specific topic name]
        SUBJECT: [subject area]
        REASON: [explanation for why this topic is recommended]
        PRIORITY: [1 for high priority, 2 for medium, 3 for low]
        """
    
    def _parse_lesson_response(self, content: str, subject: str, topic: str) -> Dict[str, Any]:
        """Parse GPT lesson response into structured format."""
        try:
            # Split content into lesson and quiz sections
            parts = content.split("QUIZ QUESTIONS:")
            lesson_content = parts[0].replace("LESSON CONTENT:", "").strip()
            
            quiz_questions = []
            if len(parts) > 1:
                quiz_section = parts[1].strip()
                # Parse quiz questions (simplified parsing)
                questions = quiz_section.split("Q")[1:]  # Skip empty first element
                
                for i, q_text in enumerate(questions):
                    if q_text.strip():
                        lines = q_text.strip().split('\n')
                        question_text = lines[0].split(':', 1)[1].strip() if ':' in lines[0] else lines[0].strip()
                        
                        question = {
                            "id": f"q{i+1}",
                            "question": question_text,
                            "type": "short_answer",  # Default
                            "options": None,
                            "correct_answer": ""
                        }
                        
                        for line in lines[1:]:
                            if line.startswith("Type:"):
                                question["type"] = line.split(":", 1)[1].strip()
                            elif line.startswith("Options:"):
                                options_text = line.split(":", 1)[1].strip()
                                question["options"] = [opt.strip() for opt in options_text.split(",")]
                            elif line.startswith("Answer:"):
                                question["correct_answer"] = line.split(":", 1)[1].strip()
                        
                        quiz_questions.append(question)
            
            return {
                "content": lesson_content,
                "quiz_questions": quiz_questions,
                "subject": subject,
                "topic": topic
            }
            
        except Exception as e:
            # Fallback parsing
            return {
                "content": content,
                "quiz_questions": [],
                "subject": subject,
                "topic": topic
            }
    
    def _parse_evaluation_response(self, content: str, total_questions: int) -> Dict[str, Any]:
        """Parse GPT evaluation response into structured format."""
        try:
            lines = content.split('\n')
            score = 0
            percentage = 0
            strengths = []
            weaknesses = []
            feedback = ""
            
            current_section = None
            for line in lines:
                line = line.strip()
                if line.startswith("SCORE:"):
                    score_text = line.split(":", 1)[1].strip()
                    score = int(score_text.split()[0]) if score_text.split() else 0
                elif line.startswith("PERCENTAGE:"):
                    percentage_text = line.split(":", 1)[1].strip()
                    percentage = int(percentage_text.replace("%", "")) if percentage_text else 0
                elif line.startswith("STRENGTHS:"):
                    current_section = "strengths"
                elif line.startswith("WEAKNESSES:"):
                    current_section = "weaknesses"
                elif line.startswith("FEEDBACK:") or line.startswith("OVERALL COMMENT:"):
                    current_section = "feedback"
                elif line.startswith("- ") and current_section == "strengths":
                    strengths.append(line[2:])
                elif line.startswith("- ") and current_section == "weaknesses":
                    weaknesses.append(line[2:])
                elif current_section == "feedback" and line:
                    feedback += line + " "
            
            return {
                "score": percentage,
                "total_questions": total_questions,
                "correct_count": score,
                "feedback": feedback.strip(),
                "strengths": strengths,
                "weaknesses": weaknesses
            }
            
        except Exception as e:
            # Fallback evaluation
            return {
                "score": 50,
                "total_questions": total_questions,
                "correct_count": total_questions // 2,
                "feedback": "Quiz completed. Keep practicing!",
                "strengths": ["Attempted all questions"],
                "weaknesses": ["Review the material"]
            }
    
    def _parse_recommendation_response(self, content: str) -> Dict[str, Any]:
        """Parse GPT recommendation response into structured format."""
        try:
            lines = content.split('\n')
            recommendation = {
                "next_topic": "",
                "subject": "",
                "reason": "",
                "priority": 1
            }
            
            for line in lines:
                line = line.strip()
                if line.startswith("RECOMMENDED TOPIC:"):
                    recommendation["next_topic"] = line.split(":", 1)[1].strip()
                elif line.startswith("SUBJECT:"):
                    recommendation["subject"] = line.split(":", 1)[1].strip()
                elif line.startswith("REASON:"):
                    recommendation["reason"] = line.split(":", 1)[1].strip()
                elif line.startswith("PRIORITY:"):
                    priority_text = line.split(":", 1)[1].strip()
                    recommendation["priority"] = int(priority_text[0]) if priority_text and priority_text[0].isdigit() else 1
            
            return recommendation
            
        except Exception as e:
            # Fallback recommendation
            return {
                "next_topic": "Review fundamentals",
                "subject": "General",
                "reason": "Continue building foundational knowledge",
                "priority": 1
            }
