from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class ProgressCreate(BaseModel):
    subject: str
    topic: str
    score: int
    lesson_content: Optional[str] = None

class ProgressResponse(BaseModel):
    id: int
    user_id: int
    subject: str
    topic: str
    score: int
    timestamp: datetime
    lesson_content: Optional[str] = None
    
    class Config:
        from_attributes = True

class ProgressSummary(BaseModel):
    user_id: int
    total_lessons: int
    average_score: float
    subjects: List[str]
    recent_progress: List[ProgressResponse]
    
class RecommendationCreate(BaseModel):
    next_topic: str
    subject: str
    reason: Optional[str] = None
    priority: int = 1

class RecommendationResponse(BaseModel):
    id: int
    user_id: int
    next_topic: str
    subject: str
    reason: Optional[str] = None
    priority: int
    created_at: datetime
    is_completed: int
    
    class Config:
        from_attributes = True
