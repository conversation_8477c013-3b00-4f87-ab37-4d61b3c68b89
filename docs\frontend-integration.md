# Frontend Integration Guide

This guide provides detailed instructions for integrating the Educational Tutor API with no-code platforms like Glide and Bubble.io.

## API Base URL

```
Production: https://your-api-domain.com/api/v1
Development: http://localhost:8000/api/v1
```

## Authentication

All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## Core Integration Workflow

### 1. User Authentication Flow

#### Register New User
```http
POST /auth/register
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "securepassword",
  "level": "beginner"
}
```

**Response:**
```json
{
  "id": 1,
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "level": "beginner",
  "created_at": "2024-01-01T10:00:00Z"
}
```

#### Login User
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "level": "beginner"
  }
}
```

### 2. Tutoring System Integration

#### Start New Lesson
```http
POST /tutor/lesson/start
Authorization: Bearer <token>
Content-Type: application/json

{
  "subject": "Mathematics",
  "topic": "Basic Arithmetic",
  "user_level": "beginner"
}
```

**Response:**
```json
{
  "content": "Let's learn about basic arithmetic...",
  "quiz_questions": [
    {
      "id": "q1",
      "question": "What is 2 + 2?",
      "type": "multiple_choice",
      "options": ["A) 3", "B) 4", "C) 5", "D) 6"],
      "correct_answer": "B) 4"
    }
  ],
  "lesson_id": "uuid-lesson-id",
  "subject": "Mathematics",
  "topic": "Basic Arithmetic"
}
```

#### Continue Lesson
```http
POST /tutor/lesson/next
Authorization: Bearer <token>
Content-Type: application/json

{
  "subject": "Mathematics",
  "topic": "Basic Arithmetic",
  "user_level": "beginner",
  "previous_content": "Previous lesson content...",
  "user_response": "I understand addition but need help with subtraction"
}
```

### 3. Quiz System Integration

#### Submit Quiz Answers
```http
POST /quiz/submit
Authorization: Bearer <token>
Content-Type: application/json

{
  "quiz_id": "uuid-lesson-id",
  "subject": "Mathematics",
  "topic": "Basic Arithmetic",
  "answers": {
    "q1": "B) 4",
    "q2": "A) 1",
    "q3": "C) 6"
  }
}
```

**Response:**
```json
{
  "score": 85,
  "total_questions": 3,
  "correct_count": 2,
  "feedback": "Great job! You got most questions correct...",
  "strengths": ["Good understanding of addition"],
  "weaknesses": ["Review subtraction concepts"],
  "detailed_results": [
    {
      "question_id": "q1",
      "question": "What is 2 + 2?",
      "user_answer": "B) 4",
      "correct_answer": "B) 4",
      "is_correct": true,
      "explanation": "The correct answer is: B) 4"
    }
  ]
}
```

### 4. Progress Tracking

#### Save Progress
```http
POST /progress/save
Authorization: Bearer <token>
Content-Type: application/json

{
  "subject": "Mathematics",
  "topic": "Basic Arithmetic",
  "score": 85,
  "lesson_content": "Completed lesson on addition and subtraction"
}
```

#### Get Progress Summary
```http
GET /progress/summary
Authorization: Bearer <token>
```

**Response:**
```json
{
  "user_id": 1,
  "total_lessons": 15,
  "average_score": 82.5,
  "subjects": ["Mathematics", "Science", "English"],
  "recent_progress": [
    {
      "id": 1,
      "subject": "Mathematics",
      "topic": "Basic Arithmetic",
      "score": 85,
      "timestamp": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### 5. Recommendations

#### Get Next Topic Recommendations
```http
GET /recommend/next
Authorization: Bearer <token>
```

**Response:**
```json
[
  {
    "type": "ai_generated",
    "subject": "Mathematics",
    "next_topic": "Multiplication Basics",
    "reason": "Strong performance in addition suggests readiness for multiplication",
    "priority": 1
  },
  {
    "type": "review",
    "subject": "Mathematics",
    "next_topic": "Review subtraction",
    "reason": "Some difficulty with subtraction problems",
    "priority": 2
  }
]
```

## Platform-Specific Integration

### Glide Integration

1. **Setup API Calls:**
   - Use Glide's "Call API" action
   - Set base URL to your API endpoint
   - Configure headers for authentication

2. **Data Storage:**
   - Store user token in Glide's user profile
   - Use Glide's data tables to cache progress data

3. **UI Components:**
   - Chat interface: Use Glide's text input and rich text components
   - Quiz interface: Use form components with radio buttons/text inputs
   - Dashboard: Use charts and list components

### Bubble.io Integration

1. **API Connector Setup:**
   - Install API Connector plugin
   - Configure API calls with proper authentication
   - Set up data types matching API responses

2. **Workflows:**
   - Create workflows for login/register
   - Set up repeating groups for progress display
   - Use conditional formatting for quiz results

3. **Data Management:**
   - Store JWT token in user data
   - Create custom states for lesson content
   - Use Bubble's database for caching

## Error Handling

Common HTTP status codes and their meanings:

- `200`: Success
- `201`: Created successfully
- `400`: Bad request (validation error)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not found
- `500`: Internal server error

## Rate Limiting

- Authentication endpoints: 5 requests per minute
- Tutoring endpoints: 10 requests per minute
- Other endpoints: 60 requests per minute

## Data Formats

### User Levels
- `"beginner"`: Basic level content
- `"intermediate"`: Intermediate level content  
- `"advanced"`: Advanced level content

### Quiz Question Types
- `"multiple_choice"`: Questions with predefined options
- `"short_answer"`: Open-ended text responses

### Recommendation Types
- `"starter"`: For new users
- `"continue"`: Continue current learning path
- `"review"`: Review previous topics
- `"advance"`: Move to advanced topics
- `"ai_generated"`: AI-powered recommendations

## Best Practices

1. **Token Management:**
   - Store tokens securely
   - Implement token refresh logic
   - Handle token expiration gracefully

2. **User Experience:**
   - Show loading states during API calls
   - Implement offline capabilities where possible
   - Provide clear error messages

3. **Performance:**
   - Cache frequently accessed data
   - Implement pagination for large datasets
   - Use appropriate request timeouts

4. **Security:**
   - Never expose API keys in frontend code
   - Validate user inputs before sending to API
   - Implement proper CORS settings

## Testing

Use the provided test endpoints to verify integration:

```http
GET /health
GET /
```

For detailed API documentation, visit:
- Swagger UI: `{base_url}/docs`
- ReDoc: `{base_url}/redoc`
