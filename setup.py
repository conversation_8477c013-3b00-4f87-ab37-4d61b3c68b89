#!/usr/bin/env python3
"""
Educational Tutor API - Setup Script
Run this script to set up the development environment.
"""

import os
import subprocess
import sys
import secrets

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def create_env_file():
    """Create .env file from template if it doesn't exist."""
    if os.path.exists(".env"):
        print("✅ .env file already exists")
        return True
    
    if not os.path.exists(".env.example"):
        print("❌ .env.example file not found")
        return False
    
    print("📝 Creating .env file from template...")
    
    # Read template
    with open(".env.example", "r") as f:
        content = f.read()
    
    # Generate secure secret key
    secret_key = secrets.token_urlsafe(32)
    content = content.replace("your_secret_key_here", secret_key)
    
    # Write .env file
    with open(".env", "w") as f:
        f.write(content)
    
    print("✅ .env file created successfully")
    print("🔑 Please add your OpenAI API key to the .env file")
    return True

def main():
    """Main setup function."""
    print("🎓 Educational Tutor API - Setup")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Create .env file
    if not create_env_file():
        print("❌ Failed to create .env file")
        sys.exit(1)
    
    # Create database tables
    print("🗄️  Initializing database...")
    try:
        from app.db.database import engine
        from app.db import models
        models.Base.metadata.create_all(bind=engine)
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        sys.exit(1)
    
    print("\n" + "=" * 40)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Add your OpenAI API key to the .env file")
    print("2. Run 'python run.py' to start the development server")
    print("3. Visit http://localhost:8000/docs for API documentation")
    print("\n🔗 Useful commands:")
    print("• Start server: python run.py")
    print("• Run tests: pytest")
    print("• View docs: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
