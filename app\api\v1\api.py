from fastapi import APIRouter
from app.api.v1.endpoints import auth, tutor, quiz, progress, recommendations

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(tutor.router, prefix="/tutor", tags=["tutoring"])
api_router.include_router(quiz.router, prefix="/quiz", tags=["quizzes"])
api_router.include_router(progress.router, prefix="/progress", tags=["progress"])
api_router.include_router(recommendations.router, prefix="/recommend", tags=["recommendations"])
