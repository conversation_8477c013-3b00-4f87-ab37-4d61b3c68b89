"""
Demo content for the Educational Tutor when OpenAI API is not available.
This provides comprehensive educational content across multiple subjects.
"""

DEMO_LESSONS = {
    "Mathematics": {
        "Basic Arithmetic": {
            "beginner": {
                "content": """
# Basic Arithmetic - Foundation of Mathematics

Welcome to the world of numbers! Arithmetic is the foundation of all mathematics, and mastering these operations will set you up for success in more advanced topics.

## The Four Basic Operations

### 1. Addition (+)
Addition means combining numbers to find their total.
- Example: 5 + 3 = 8
- Think of it as "putting together"
- The numbers being added are called "addends"
- The result is called the "sum"

### 2. Subtraction (-)
Subtraction means taking away one number from another.
- Example: 10 - 4 = 6
- Think of it as "taking away"
- The first number is the "minuend"
- The second number is the "subtrahend"
- The result is called the "difference"

### 3. Multiplication (×)
Multiplication is repeated addition.
- Example: 4 × 3 = 12 (same as 4 + 4 + 4)
- The numbers being multiplied are called "factors"
- The result is called the "product"

### 4. Division (÷)
Division means splitting a number into equal parts.
- Example: 15 ÷ 3 = 5
- The number being divided is the "dividend"
- The number you divide by is the "divisor"
- The result is called the "quotient"

## Practice Tips
1. Start with small numbers
2. Use visual aids like counting objects
3. Practice daily for 10-15 minutes
4. Check your work by using the opposite operation

Ready to test your knowledge?
                """,
                "questions": [
                    {
                        "id": "q1",
                        "question": "What is 7 + 5?",
                        "type": "multiple_choice",
                        "options": ["A) 11", "B) 12", "C) 13", "D) 14"],
                        "correct_answer": "B) 12"
                    },
                    {
                        "id": "q2",
                        "question": "What is 20 - 8?",
                        "type": "multiple_choice",
                        "options": ["A) 10", "B) 11", "C) 12", "D) 13"],
                        "correct_answer": "C) 12"
                    },
                    {
                        "id": "q3",
                        "question": "What is 6 × 4?",
                        "type": "short_answer",
                        "correct_answer": "24"
                    }
                ]
            },
            "intermediate": {
                "content": """
# Advanced Arithmetic Operations

Now that you understand basic operations, let's explore more complex arithmetic concepts and problem-solving strategies.

## Order of Operations (PEMDAS/BODMAS)
When solving expressions with multiple operations, follow this order:
1. **P**arentheses (or **B**rackets)
2. **E**xponents (or **O**rders)
3. **M**ultiplication and **D**ivision (left to right)
4. **A**ddition and **S**ubtraction (left to right)

Example: 2 + 3 × 4 = 2 + 12 = 14 (not 20!)

## Working with Larger Numbers
- Break down complex problems into smaller steps
- Use estimation to check if your answer makes sense
- Practice mental math techniques

## Word Problems Strategy
1. Read the problem carefully
2. Identify what you need to find
3. Determine which operation(s) to use
4. Solve step by step
5. Check your answer

## Fractions and Decimals Introduction
- Fractions represent parts of a whole
- Decimals are another way to express fractions
- 1/2 = 0.5, 1/4 = 0.25, 3/4 = 0.75

Let's practice with some challenging problems!
                """,
                "questions": [
                    {
                        "id": "q1",
                        "question": "What is 5 + 3 × 2?",
                        "type": "multiple_choice",
                        "options": ["A) 11", "B) 16", "C) 13", "D) 10"],
                        "correct_answer": "A) 11"
                    },
                    {
                        "id": "q2",
                        "question": "If you have 24 apples and want to share them equally among 6 friends, how many apples does each friend get?",
                        "type": "short_answer",
                        "correct_answer": "4"
                    }
                ]
            }
        },
        "Algebra": {
            "intermediate": {
                "content": """
# Introduction to Algebra

Algebra is the branch of mathematics that uses letters and symbols to represent numbers and express mathematical relationships.

## What is a Variable?
A variable is a letter (usually x, y, or z) that represents an unknown number.
- In x + 3 = 7, the variable x represents the number 4
- Variables allow us to solve problems with unknown quantities

## Basic Algebraic Expressions
- **Term**: A single number, variable, or product (like 3x, -5, or 7y)
- **Expression**: A combination of terms (like 2x + 5 or 3y - 7)
- **Equation**: An expression with an equals sign (like x + 4 = 10)

## Solving Simple Equations
To solve x + 5 = 12:
1. We want to isolate x
2. Subtract 5 from both sides: x + 5 - 5 = 12 - 5
3. Simplify: x = 7

## The Golden Rule
Whatever you do to one side of an equation, you must do to the other side!

## Common Operations
- Addition: x + 3
- Subtraction: x - 5
- Multiplication: 3x (means 3 times x)
- Division: x/4 (means x divided by 4)

## Real-World Applications
Algebra helps us solve practical problems:
- Finding unknown quantities
- Calculating costs and profits
- Determining time and distance
- Planning and budgeting

Ready to solve some equations?
                """,
                "questions": [
                    {
                        "id": "q1",
                        "question": "If x + 7 = 15, what is x?",
                        "type": "multiple_choice",
                        "options": ["A) 6", "B) 7", "C) 8", "D) 9"],
                        "correct_answer": "C) 8"
                    },
                    {
                        "id": "q2",
                        "question": "What is the value of 2x when x = 6?",
                        "type": "short_answer",
                        "correct_answer": "12"
                    }
                ]
            }
        }
    },
    "Science": {
        "Physics Basics": {
            "beginner": {
                "content": """
# Introduction to Physics

Physics is the study of how things move and interact in our universe. It helps us understand everything from why apples fall to how rockets fly!

## What is Matter?
Everything around you is made of matter:
- **Mass**: How much stuff is in an object
- **Volume**: How much space an object takes up
- **Density**: How tightly packed the matter is

## Forms of Energy
Energy is the ability to make things happen:

### 1. Kinetic Energy
- Energy of motion
- A moving car has kinetic energy
- The faster something moves, the more kinetic energy it has

### 2. Potential Energy
- Stored energy
- A ball held up high has potential energy
- When released, it converts to kinetic energy

### 3. Other Forms
- **Heat Energy**: Makes things warm
- **Light Energy**: Helps us see
- **Sound Energy**: Lets us hear
- **Electrical Energy**: Powers our devices

## Forces Around Us
A force is a push or pull:

### Gravity
- Pulls everything toward Earth
- Why things fall down
- Keeps us on the ground

### Friction
- Slows down moving objects
- Why a ball eventually stops rolling
- Helps us walk without slipping

### Magnetism
- Attracts or repels magnetic materials
- Used in compasses and motors

## Simple Machines
Tools that make work easier:
- **Lever**: Like a seesaw
- **Wheel and Axle**: Like a bicycle wheel
- **Pulley**: Helps lift heavy objects
- **Inclined Plane**: A ramp

Physics is everywhere in your daily life!
                """,
                "questions": [
                    {
                        "id": "q1",
                        "question": "What type of energy does a moving bicycle have?",
                        "type": "multiple_choice",
                        "options": ["A) Potential energy", "B) Kinetic energy", "C) Heat energy", "D) Light energy"],
                        "correct_answer": "B) Kinetic energy"
                    },
                    {
                        "id": "q2",
                        "question": "What force pulls objects toward Earth?",
                        "type": "short_answer",
                        "correct_answer": "Gravity"
                    }
                ]
            }
        },
        "Chemistry Basics": {
            "intermediate": {
                "content": """
# Introduction to Chemistry

Chemistry is the study of matter and the changes it undergoes. It's like cooking - you mix ingredients (elements) to create new substances (compounds)!

## Atoms - The Building Blocks
Everything is made of tiny particles called atoms:
- **Protons**: Positive charge, in the nucleus
- **Neutrons**: No charge, in the nucleus
- **Electrons**: Negative charge, orbit the nucleus

## Elements and the Periodic Table
- **Element**: A pure substance made of one type of atom
- **Periodic Table**: Organizes all known elements
- Examples: Hydrogen (H), Oxygen (O), Carbon (C)

## Compounds and Molecules
- **Compound**: Two or more elements chemically combined
- **Molecule**: The smallest unit of a compound
- Water (H₂O) = 2 Hydrogen atoms + 1 Oxygen atom

## States of Matter
Matter exists in different states:

### Solid
- Particles tightly packed
- Fixed shape and volume
- Example: Ice

### Liquid
- Particles loosely packed
- Fixed volume, takes shape of container
- Example: Water

### Gas
- Particles spread out
- No fixed shape or volume
- Example: Water vapor

## Chemical vs Physical Changes

### Physical Changes
- No new substance formed
- Can usually be reversed
- Examples: Melting ice, cutting paper

### Chemical Changes
- New substance formed
- Usually cannot be easily reversed
- Examples: Burning wood, rusting metal

## Safety in Chemistry
- Always wear safety equipment
- Never mix unknown chemicals
- Follow instructions carefully
- Wash hands after experiments

Chemistry helps us understand cooking, medicine, and technology!
                """,
                "questions": [
                    {
                        "id": "q1",
                        "question": "What is the chemical formula for water?",
                        "type": "multiple_choice",
                        "options": ["A) H₂O", "B) CO₂", "C) NaCl", "D) O₂"],
                        "correct_answer": "A) H₂O"
                    },
                    {
                        "id": "q2",
                        "question": "What type of change occurs when ice melts into water?",
                        "type": "multiple_choice",
                        "options": ["A) Chemical change", "B) Physical change", "C) Nuclear change", "D) Biological change"],
                        "correct_answer": "B) Physical change"
                    }
                ]
            }
        }
    },
    "English": {
        "Grammar Basics": {
            "beginner": {
                "content": """
# English Grammar Fundamentals

Grammar is the set of rules that helps us communicate clearly and effectively. Think of it as the blueprint for building sentences!

## Parts of Speech

### 1. Nouns
Words that name people, places, things, or ideas:
- **Person**: teacher, student, doctor
- **Place**: school, park, city
- **Thing**: book, car, computer
- **Idea**: happiness, freedom, love

### 2. Verbs
Action words or words that show a state of being:
- **Action verbs**: run, jump, write, think
- **Being verbs**: am, is, are, was, were

### 3. Adjectives
Words that describe nouns:
- **Size**: big, small, tiny, huge
- **Color**: red, blue, green, yellow
- **Quality**: beautiful, ugly, smart, funny

### 4. Pronouns
Words that replace nouns:
- **Personal**: I, you, he, she, it, we, they
- **Possessive**: my, your, his, her, its, our, their

## Sentence Structure

### Basic Sentence Pattern
**Subject + Verb + Object**
- "The cat (subject) chased (verb) the mouse (object)."

### Types of Sentences
1. **Statement**: "The sky is blue."
2. **Question**: "What time is it?"
3. **Command**: "Close the door."
4. **Exclamation**: "What a beautiful day!"

## Capitalization Rules
- First word of a sentence
- Proper nouns (names of specific people, places)
- The word "I"
- Beginning of quotes

## Punctuation Basics
- **Period (.)**: Ends statements
- **Question mark (?)**: Ends questions
- **Exclamation point (!)**: Shows strong emotion
- **Comma (,)**: Separates items in a list

## Common Mistakes to Avoid
- Mixing up "your" and "you're"
- Forgetting to capitalize proper nouns
- Run-on sentences (too long without punctuation)

Practice makes perfect in grammar!
                """,
                "questions": [
                    {
                        "id": "q1",
                        "question": "In the sentence 'The happy dog ran quickly,' what is the adjective?",
                        "type": "multiple_choice",
                        "options": ["A) dog", "B) happy", "C) ran", "D) quickly"],
                        "correct_answer": "B) happy"
                    },
                    {
                        "id": "q2",
                        "question": "What punctuation mark ends a question?",
                        "type": "short_answer",
                        "correct_answer": "Question mark"
                    }
                ]
            }
        }
    }
}

def get_demo_lesson(subject: str, topic: str, level: str = "beginner") -> dict:
    """Get demo lesson content for the specified subject, topic, and level."""
    
    # Try to find exact match
    if (subject in DEMO_LESSONS and 
        topic in DEMO_LESSONS[subject] and 
        level in DEMO_LESSONS[subject][topic]):
        lesson_data = DEMO_LESSONS[subject][topic][level]
        return {
            "content": lesson_data["content"].strip(),
            "quiz_questions": lesson_data["questions"],
            "subject": subject,
            "topic": topic
        }
    
    # Try to find topic with any level
    if subject in DEMO_LESSONS and topic in DEMO_LESSONS[subject]:
        available_levels = list(DEMO_LESSONS[subject][topic].keys())
        lesson_data = DEMO_LESSONS[subject][topic][available_levels[0]]
        return {
            "content": lesson_data["content"].strip(),
            "quiz_questions": lesson_data["questions"],
            "subject": subject,
            "topic": topic
        }
    
    # Generate generic lesson
    return {
        "content": f"""
# {topic} in {subject}

Welcome to your lesson on {topic}! This is an introductory lesson designed for {level} level students.

## Learning Objectives
By the end of this lesson, you will be able to:
1. Understand the basic concepts of {topic}
2. Apply these concepts to solve problems
3. Recognize real-world applications
4. Build confidence in {subject}

## Introduction
{topic} is an important area of study in {subject}. It provides foundational knowledge that will help you understand more advanced concepts later.

## Key Concepts
- Fundamental principles of {topic}
- Common applications and examples
- Problem-solving strategies
- Best practices and tips

## Practice and Application
The best way to master {topic} is through regular practice and application. Start with simple examples and gradually work your way up to more complex problems.

## Summary
{topic} is a valuable skill that will serve you well in your studies and beyond. Remember to practice regularly and don't hesitate to review material as needed.

Ready to test your understanding?
        """.strip(),
        "quiz_questions": [
            {
                "id": "q1",
                "question": f"What is the main focus of studying {topic}?",
                "type": "short_answer",
                "correct_answer": f"Understanding the fundamental concepts of {topic}"
            },
            {
                "id": "q2",
                "question": f"Why is {topic} important in {subject}?",
                "type": "short_answer",
                "correct_answer": f"It provides foundational knowledge for {subject}"
            }
        ],
        "subject": subject,
        "topic": topic
    }
