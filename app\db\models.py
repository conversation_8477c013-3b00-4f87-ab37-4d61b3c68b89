from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, index=True, nullable=False)
    level = Column(String(50), nullable=False, default="beginner")  # beginner, intermediate, advanced
    hashed_password = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    progress_records = relationship("Progress", back_populates="user")
    quiz_results = relationship("QuizResult", back_populates="user")
    recommendations = relationship("Recommendation", back_populates="user")

class Progress(Base):
    __tablename__ = "progress"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    subject = Column(String(100), nullable=False)  # Math, Science, History, etc.
    topic = Column(String(200), nullable=False)    # Specific topic within subject
    score = Column(Integer, nullable=False)        # Score out of 100
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    lesson_content = Column(Text, nullable=True)   # Store lesson content for reference
    
    # Relationships
    user = relationship("User", back_populates="progress_records")

class QuizResult(Base):
    __tablename__ = "quiz_results"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    quiz_id = Column(String(100), nullable=False)  # Unique identifier for quiz
    subject = Column(String(100), nullable=False)
    topic = Column(String(200), nullable=False)
    answers = Column(Text, nullable=False)         # JSON string of user answers
    correct_answers = Column(Text, nullable=True)  # JSON string of correct answers
    score = Column(Integer, nullable=False)        # Score out of 100
    total_questions = Column(Integer, nullable=False, default=0)
    correct_count = Column(Integer, nullable=False, default=0)
    feedback = Column(Text, nullable=True)         # AI-generated feedback
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="quiz_results")

class Recommendation(Base):
    __tablename__ = "recommendations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    next_topic = Column(String(200), nullable=False)
    subject = Column(String(100), nullable=False)
    reason = Column(Text, nullable=True)           # AI explanation for recommendation
    priority = Column(Integer, nullable=False, default=1)  # 1=high, 2=medium, 3=low
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    is_completed = Column(Integer, nullable=False, default=0)  # 0=pending, 1=completed
    
    # Relationships
    user = relationship("User", back_populates="recommendations")
